"use client"

import { useState, useEffect } from 'react'
import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation'
import Link from 'next/link'
import Image from 'next/image'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Separator } from '@/components/ui/separator'
import { Edit, ArrowLeft, Calendar, Star, Tag } from 'lucide-react'
import { formatDate } from '@/lib/utils'
import { toast } from 'sonner'

interface GalleryItem {
  id: string
  title: string
  description?: string
  image: string
  category?: string
  tags: string[]
  featured: boolean
  status: 'ACTIVE' | 'INACTIVE' | 'ARCHIVED'
  createdAt: string
  updatedAt: string
}

export default function GalleryViewPage() {
  const params = useParams()
  const router = useRouter()
  const [galleryItem, setGalleryItem] = useState<GalleryItem | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const fetchGalleryItem = async () => {
      try {
        const response = await fetch(`/api/gallery/${params.id}`)
        if (response.ok) {
          const data = await response.json()
          setGalleryItem(data)
        } else {
          toast.error('Gallery item not found')
          router.push('/dashboard/gallery')
        }
      } catch (error) {
        toast.error('Error fetching gallery item')
        router.push('/dashboard/gallery')
      } finally {
        setLoading(false)
      }
    }

    if (params.id) {
      fetchGalleryItem()
    }
  }, [params.id, router])

  if (loading) {
    return <div>Loading...</div>
  }

  if (!galleryItem) {
    return <div>Gallery item not found</div>
  }

  const getStatusBadge = (status: string) => {
    const variants = {
      ACTIVE: 'default',
      INACTIVE: 'secondary',
      ARCHIVED: 'outline',
    } as const

    return (
      <Badge variant={variants[status as keyof typeof variants]}>
        {status}
      </Badge>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="ghost" size="sm" onClick={() => router.back()}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back
          </Button>
          <div>
            <h1 className="text-3xl font-bold flex items-center gap-2">
              {galleryItem.title}
              {galleryItem.featured && (
                <Star className="h-6 w-6 text-yellow-500 fill-current" />
              )}
            </h1>
            <div className="flex items-center gap-4 text-sm text-muted-foreground mt-2">
              <div className="flex items-center gap-1">
                <Calendar className="h-4 w-4" />
                Added {formatDate(galleryItem.createdAt)}
              </div>
            </div>
          </div>
        </div>
        <div className="flex items-center gap-2">
          {getStatusBadge(galleryItem.status)}
          {galleryItem.category && (
            <Badge variant="outline">{galleryItem.category}</Badge>
          )}
          <Button asChild>
            <Link href={`/dashboard/gallery/${galleryItem.id}/edit`}>
              <Edit className="h-4 w-4 mr-2" />
              Edit
            </Link>
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div className="lg:col-span-2 space-y-6">
          <Card>
            <CardContent className="p-0">
              <div className="relative aspect-square w-full overflow-hidden rounded-lg">
                <Image
                  src={galleryItem.image}
                  alt={galleryItem.title}
                  fill
                  className="object-cover"
                />
              </div>
            </CardContent>
          </Card>

          {galleryItem.description && (
            <Card>
              <CardHeader>
                <CardTitle>Description</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground leading-relaxed">
                  {galleryItem.description}
                </p>
              </CardContent>
            </Card>
          )}
        </div>

        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Details</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label className="text-sm font-medium">Status</Label>
                <div className="mt-1">
                  {getStatusBadge(galleryItem.status)}
                </div>
              </div>

              {galleryItem.category && (
                <div>
                  <Label className="text-sm font-medium">Category</Label>
                  <div className="mt-1">
                    <Badge variant="outline">{galleryItem.category}</Badge>
                  </div>
                </div>
              )}

              {galleryItem.tags.length > 0 && (
                <div>
                  <Label className="text-sm font-medium">Tags</Label>
                  <div className="flex flex-wrap gap-2 mt-1">
                    {galleryItem.tags.map((tag, index) => (
                      <Badge key={index} variant="outline">
                        <Tag className="h-3 w-3 mr-1" />
                        {tag}
                      </Badge>
                    ))}
                  </div>
                </div>
              )}

              <div>
                <Label className="text-sm font-medium">Featured</Label>
                <div className="mt-1">
                  <Badge variant={galleryItem.featured ? "default" : "outline"}>
                    {galleryItem.featured ? "Yes" : "No"}
                  </Badge>
                </div>
              </div>

              <Separator />

              <div className="space-y-2 text-sm">
                <div>
                  <span className="font-medium">Added:</span>{' '}
                  {formatDate(galleryItem.createdAt)}
                </div>
                <div>
                  <span className="font-medium">Updated:</span>{' '}
                  {formatDate(galleryItem.updatedAt)}
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Quick Actions</CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              <Button asChild className="w-full">
                <Link href={`/dashboard/gallery/${galleryItem.id}/edit`}>
                  <Edit className="h-4 w-4 mr-2" />
                  Edit Image
                </Link>
              </Button>
              <Button variant="outline" className="w-full" asChild>
                <Link href="/dashboard/gallery">
                  View All Gallery
                </Link>
              </Button>
              <Button variant="outline" className="w-full" asChild>
                <Link href="/dashboard/gallery/new">
                  Add New Image
                </Link>
              </Button>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Image Info</CardTitle>
            </CardHeader>
            <CardContent className="space-y-2 text-sm text-muted-foreground">
              <p>• This image is part of your portfolio gallery</p>
              <p>• {galleryItem.featured ? 'Featured images appear prominently' : 'Mark as featured to highlight'}</p>
              <p>• Only active images are visible to visitors</p>
              <p>• Use tags and categories for better organization</p>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}

function Label({ children, className }: { children: React.ReactNode; className?: string }) {
  return <div className={className}>{children}</div>
}
