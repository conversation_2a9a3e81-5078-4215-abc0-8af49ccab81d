import { Metadata } from 'next'
import AboutHero from '@/components/sections/about-hero'
import AboutStory from '@/components/sections/about-story'
import AboutExperience from '@/components/sections/about-experience'
import AboutCities from '@/components/sections/about-cities'
import AboutCTA from '@/components/sections/about-cta'
import { getSiteConfig } from '@/lib/data'

const siteConfig = getSiteConfig()

export const metadata: Metadata = {
  title: `About ${siteConfig.site.name} | Professional Makeup Artist in Biratnagar`,
  description: 'Learn about our professional makeup artist, her journey, expertise, and passion for enhancing natural beauty across Nepal.',
  openGraph: {
    title: `About ${siteConfig.site.name} | Professional Makeup Artist`,
    description: 'Learn about our professional makeup artist, her journey, expertise, and passion for enhancing natural beauty across Nepal.',
    url: `${siteConfig.site.url}/about`,
  },
}

export default function AboutPage() {
  return (
    <>
      <AboutHero />
      <AboutStory />
      <AboutExperience />
      <AboutCities />
      <AboutCTA />
    </>
  )
}
