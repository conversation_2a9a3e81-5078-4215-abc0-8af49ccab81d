import { Metadata } from 'next'
import PortfolioHero from '@/components/sections/portfolio-hero'
import PortfolioGallery from '@/components/sections/portfolio-gallery'
import PortfolioCTA from '@/components/sections/portfolio-cta'
import { getSiteConfig } from '@/lib/data'

const siteConfig = getSiteConfig()

export const metadata: Metadata = {
  title: `Portfolio & Gallery | ${siteConfig.site.name}`,
  description: 'Browse our stunning portfolio of makeup transformations. See our work in bridal, party, traditional, and photoshoot makeup across Nepal.',
  openGraph: {
    title: `Portfolio & Gallery | ${siteConfig.site.name}`,
    description: 'Browse our stunning portfolio of makeup transformations. See our work in bridal, party, traditional, and photoshoot makeup across Nepal.',
    url: `${siteConfig.site.url}/portfolio`,
  },
}

export default function PortfolioPage() {
  return (
    <>
      <PortfolioHero />
      <PortfolioGallery />
      <PortfolioCTA />
    </>
  )
}
