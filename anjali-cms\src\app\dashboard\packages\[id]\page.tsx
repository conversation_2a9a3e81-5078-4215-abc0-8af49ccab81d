"use client"

import { useState, useEffect } from 'react'
import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation'
import Link from 'next/link'
import Image from 'next/image'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Separator } from '@/components/ui/separator'
import { Edit, ArrowLeft, Calendar, Clock, DollarSign, Star, CheckCircle, Package } from 'lucide-react'
import { formatDate } from '@/lib/utils'
import { toast } from 'sonner'

interface PackageData {
  id: string
  name: string
  slug: string
  description: string
  services: string[]
  features: string[]
  price: string
  originalPrice?: string
  duration?: string
  popular: boolean
  image?: string
  category?: string
  status: 'ACTIVE' | 'INACTIVE' | 'ARCHIVED'
  createdAt: string
  updatedAt: string
}

export default function PackageViewPage() {
  const params = useParams()
  const router = useRouter()
  const [packageData, setPackageData] = useState<PackageData | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const fetchPackage = async () => {
      try {
        const response = await fetch(`/api/packages/${params.id}`)
        if (response.ok) {
          const data = await response.json()
          setPackageData(data)
        } else {
          toast.error('Package not found')
          router.push('/dashboard/packages')
        }
      } catch (error) {
        toast.error('Error fetching package')
        router.push('/dashboard/packages')
      } finally {
        setLoading(false)
      }
    }

    if (params.id) {
      fetchPackage()
    }
  }, [params.id, router])

  if (loading) {
    return <div>Loading...</div>
  }

  if (!packageData) {
    return <div>Package not found</div>
  }

  const getStatusBadge = (status: string) => {
    const variants = {
      ACTIVE: 'default',
      INACTIVE: 'secondary',
      ARCHIVED: 'outline',
    } as const

    return (
      <Badge variant={variants[status as keyof typeof variants]}>
        {status}
      </Badge>
    )
  }

  const hasDiscount = packageData.originalPrice && packageData.originalPrice !== packageData.price

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="ghost" size="sm" onClick={() => router.back()}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back
          </Button>
          <div>
            <h1 className="text-3xl font-bold flex items-center gap-2">
              {packageData.name}
              {packageData.popular && (
                <Star className="h-6 w-6 text-yellow-500 fill-current" />
              )}
            </h1>
            <div className="flex items-center gap-4 text-sm text-muted-foreground mt-2">
              <div className="flex items-center gap-1">
                <Calendar className="h-4 w-4" />
                Created {formatDate(packageData.createdAt)}
              </div>
              {packageData.duration && (
                <div className="flex items-center gap-1">
                  <Clock className="h-4 w-4" />
                  {packageData.duration}
                </div>
              )}
              <div className="flex items-center gap-1">
                <DollarSign className="h-4 w-4" />
                {packageData.price}
                {hasDiscount && (
                  <span className="line-through text-muted-foreground ml-1">
                    {packageData.originalPrice}
                  </span>
                )}
              </div>
            </div>
          </div>
        </div>
        <div className="flex items-center gap-2">
          {getStatusBadge(packageData.status)}
          {packageData.category && (
            <Badge variant="outline">{packageData.category}</Badge>
          )}
          <Button asChild>
            <Link href={`/dashboard/packages/${packageData.id}/edit`}>
              <Edit className="h-4 w-4 mr-2" />
              Edit
            </Link>
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div className="lg:col-span-2 space-y-6">
          {packageData.image && (
            <Card>
              <CardContent className="p-0">
                <div className="relative aspect-video w-full overflow-hidden rounded-lg">
                  <Image
                    src={packageData.image}
                    alt={packageData.name}
                    fill
                    className="object-cover"
                  />
                </div>
              </CardContent>
            </Card>
          )}

          <Card>
            <CardHeader>
              <CardTitle>Description</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground leading-relaxed">
                {packageData.description}
              </p>
            </CardContent>
          </Card>

          {packageData.services.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle>Included Services</CardTitle>
                <CardDescription>
                  Services included in this package
                </CardDescription>
              </CardHeader>
              <CardContent>
                <ul className="space-y-2">
                  {packageData.services.map((service, index) => (
                    <li key={index} className="flex items-start gap-2">
                      <Package className="h-5 w-5 text-blue-500 mt-0.5 flex-shrink-0" />
                      <span>{service}</span>
                    </li>
                  ))}
                </ul>
              </CardContent>
            </Card>
          )}

          {packageData.features.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle>Package Features</CardTitle>
                <CardDescription>
                  Additional features and benefits
                </CardDescription>
              </CardHeader>
              <CardContent>
                <ul className="space-y-2">
                  {packageData.features.map((feature, index) => (
                    <li key={index} className="flex items-start gap-2">
                      <CheckCircle className="h-5 w-5 text-green-500 mt-0.5 flex-shrink-0" />
                      <span>{feature}</span>
                    </li>
                  ))}
                </ul>
              </CardContent>
            </Card>
          )}
        </div>

        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Package Details</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label className="text-sm font-medium">Status</Label>
                <div className="mt-1">
                  {getStatusBadge(packageData.status)}
                </div>
              </div>

              {packageData.category && (
                <div>
                  <Label className="text-sm font-medium">Category</Label>
                  <div className="mt-1">
                    <Badge variant="outline">{packageData.category}</Badge>
                  </div>
                </div>
              )}

              <div>
                <Label className="text-sm font-medium">Price</Label>
                <div className="mt-1 flex items-center gap-2">
                  <span className="text-lg font-semibold">{packageData.price}</span>
                  {hasDiscount && (
                    <span className="text-sm text-muted-foreground line-through">
                      {packageData.originalPrice}
                    </span>
                  )}
                </div>
              </div>

              {packageData.duration && (
                <div>
                  <Label className="text-sm font-medium">Duration</Label>
                  <p className="text-sm text-muted-foreground mt-1">
                    {packageData.duration}
                  </p>
                </div>
              )}

              <div>
                <Label className="text-sm font-medium">Popular Package</Label>
                <div className="mt-1">
                  <Badge variant={packageData.popular ? "default" : "outline"}>
                    {packageData.popular ? "Yes" : "No"}
                  </Badge>
                </div>
              </div>

              <Separator />

              <div className="space-y-2 text-sm">
                <div>
                  <span className="font-medium">Created:</span>{' '}
                  {formatDate(packageData.createdAt)}
                </div>
                <div>
                  <span className="font-medium">Updated:</span>{' '}
                  {formatDate(packageData.updatedAt)}
                </div>
                <div>
                  <span className="font-medium">Slug:</span>{' '}
                  <code className="text-xs bg-muted px-1 py-0.5 rounded">
                    {packageData.slug}
                  </code>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Quick Actions</CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              <Button asChild className="w-full">
                <Link href={`/dashboard/packages/${packageData.id}/edit`}>
                  <Edit className="h-4 w-4 mr-2" />
                  Edit Package
                </Link>
              </Button>
              <Button variant="outline" className="w-full" asChild>
                <Link href="/dashboard/packages">
                  View All Packages
                </Link>
              </Button>
              <Button variant="outline" className="w-full" asChild>
                <Link href="/dashboard/packages/new">
                  Create New Package
                </Link>
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}

function Label({ children, className }: { children: React.ReactNode; className?: string }) {
  return <div className={className}>{children}</div>
}
