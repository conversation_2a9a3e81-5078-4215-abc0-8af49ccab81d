'use client'

import Image from 'next/image'
import { motion } from 'framer-motion'
import { Star, Award, Users, MapPin } from 'lucide-react'
import { Badge } from '@/components/ui/badge'
import { Section } from '@/components/ui/section'
import { AnimatedElement } from '@/components/ui/animated-element'

export default function AboutHero() {
  return (
    <Section className="pt-24 pb-16">
      <div className="grid lg:grid-cols-2 gap-12 items-center">
        {/* Content */}
        <AnimatedElement animation="slideRight" className="space-y-8">
          <div className="space-y-4">
            <Badge variant="secondary" className="text-sm px-4 py-2">
              <Award className="w-4 h-4 mr-2" />
              Professional Makeup Artist
            </Badge>
            
            <h1 className="font-display text-4xl md:text-5xl lg:text-6xl font-bold text-text-primary leading-tight">
              Meet
              <span className="block text-transparent bg-gradient-to-r from-rose-gold to-blush-pink bg-clip-text">
                <PERSON><PERSON><PERSON>
              </span>
            </h1>
            
            <p className="text-xl text-text-secondary leading-relaxed">
              A passionate makeup artist dedicated to enhancing your natural beauty 
              and making every moment unforgettable. With over 5 years of experience 
              in the beauty industry, I specialize in creating stunning looks for 
              all your special occasions.
            </p>
          </div>

          {/* Stats */}
          <div className="grid grid-cols-3 gap-6">
            <div className="text-center">
              <div className="text-3xl font-bold text-text-primary mb-1">5+</div>
              <div className="text-sm text-text-secondary">Years Experience</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-text-primary mb-1">100+</div>
              <div className="text-sm text-text-secondary">Happy Clients</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-text-primary mb-1">7</div>
              <div className="text-sm text-text-secondary">Cities Served</div>
            </div>
          </div>

          {/* Highlights */}
          <div className="space-y-3">
            <div className="flex items-center gap-3">
              <Star className="w-5 h-5 text-yellow-400 fill-current" />
              <span className="text-text-secondary">5.0 Average Rating from 50+ Reviews</span>
            </div>
            <div className="flex items-center gap-3">
              <Users className="w-5 h-5 text-rose-gold-dark" />
              <span className="text-text-secondary">Trusted by Brides Across Nepal</span>
            </div>
            <div className="flex items-center gap-3">
              <MapPin className="w-5 h-5 text-rose-gold-dark" />
              <span className="text-text-secondary">Serving Biratnagar, Itahari, Dharan & More</span>
            </div>
          </div>
        </AnimatedElement>

        {/* Image */}
        <AnimatedElement animation="slideLeft" delay={0.3} className="relative">
          <div className="relative aspect-[4/5] rounded-2xl overflow-hidden shadow-2xl">
            <Image
              src="https://images.unsplash.com/photo-1596462502278-27bfdc403348?w=600&h=750&fit=crop&crop=face"
              alt="Anjali - Professional Makeup Artist"
              fill
              className="object-cover"
              priority
            />
            
            {/* Floating Elements */}
            <motion.div
              initial={{ opacity: 0, scale: 0 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.6, delay: 1 }}
              className="absolute top-6 right-6 bg-white/90 backdrop-blur-sm rounded-lg p-3 shadow-lg"
            >
              <div className="flex items-center gap-2">
                <div className="flex text-yellow-400">
                  {[...Array(5)].map((_, i) => (
                    <Star key={i} className="w-3 h-3 fill-current" />
                  ))}
                </div>
                <span className="text-xs font-medium text-text-primary">5.0</span>
              </div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, scale: 0 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.6, delay: 1.2 }}
              className="absolute bottom-6 left-6 bg-white/90 backdrop-blur-sm rounded-lg p-3 shadow-lg"
            >
              <div className="text-center">
                <div className="text-lg font-bold text-text-primary">100+</div>
                <div className="text-xs text-text-secondary">Makeup Sessions</div>
              </div>
            </motion.div>
          </div>

          {/* Decorative Elements */}
          <motion.div
            initial={{ opacity: 0, rotate: -180 }}
            animate={{ opacity: 1, rotate: 0 }}
            transition={{ duration: 1, delay: 0.8 }}
            className="absolute -top-4 -left-4 w-24 h-24 bg-gradient-to-br from-rose-gold to-blush-pink rounded-full opacity-20 blur-xl"
          ></motion.div>
          <motion.div
            initial={{ opacity: 0, rotate: 180 }}
            animate={{ opacity: 1, rotate: 0 }}
            transition={{ duration: 1, delay: 1 }}
            className="absolute -bottom-4 -right-4 w-32 h-32 bg-gradient-to-br from-lavender to-blush-pink rounded-full opacity-20 blur-xl"
          ></motion.div>
        </AnimatedElement>
      </div>
    </Section>
  )
}
