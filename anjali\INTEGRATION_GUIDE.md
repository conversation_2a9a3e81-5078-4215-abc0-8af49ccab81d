# Frontend-CMS Integration Guide

This guide explains how to connect the Anjali frontend application with the CMS backend for dynamic content management.

## 🔗 Integration Overview

The frontend now uses **TanStack Query** to fetch data from the CMS API, replacing static JSON files with dynamic content management.

## 🚀 Quick Setup

### 1. Environment Configuration

Create or update `.env.local` in the frontend directory:

```env
# CMS API Configuration
NEXT_PUBLIC_CMS_API_URL=http://localhost:3001/api

# Site Configuration  
NEXT_PUBLIC_SITE_URL=http://localhost:3000
```

### 2. Start Both Applications

**Terminal 1 - CMS Backend:**
```bash
cd anjali-cms
npm run dev
# Runs on http://localhost:3001
```

**Terminal 2 - Frontend:**
```bash
cd anjali
npm run dev  
# Runs on http://localhost:3000
```

### 3. Seed the CMS Database

```bash
cd anjali-cms
npm run db:push
npm run db:seed
```

## 📊 What's Integrated

### ✅ **Blogs**
- **Component**: `BlogGrid`, `BlogPostContent`
- **API Hooks**: `useBlogs`, `useBlogBySlug`, `useFeaturedBlogs`
- **Features**: Pagination, search, categories, featured posts

### ✅ **Services** 
- **Component**: `ServicesGrid`, `ServicesOverview`
- **API Hooks**: `useActiveServices`, `usePopularServices`
- **Features**: Service catalog, pricing, categories, WhatsApp integration

### ✅ **Packages**
- **Component**: `PackagesPreview`, `PackagesGrid`
- **API Hooks**: `useActivePackages`, `usePopularPackages`
- **Features**: Package bundles, pricing tiers, feature lists

### ✅ **Testimonials**
- **Component**: `Testimonials`
- **API Hooks**: `useApprovedTestimonials`
- **Features**: Client reviews, ratings, approval workflow

### ✅ **Gallery**
- **Component**: `GalleryShowcase`
- **API Hooks**: `useFeaturedGallery`
- **Features**: Portfolio showcase, categories, featured items

### ✅ **Site Settings**
- **API Hooks**: `useSiteInfo`, `useContactInfo`, `useSocialMedia`
- **Features**: Dynamic site configuration, contact details

## 🔧 Technical Implementation

### TanStack Query Configuration

```typescript
// Query client with optimized settings
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 60 * 1000, // 1 minute
      gcTime: 10 * 60 * 1000, // 10 minutes
      retry: (failureCount, error) => {
        if (error?.status >= 400 && error?.status < 500) {
          return false // Don't retry 4xx errors
        }
        return failureCount < 3
      },
      refetchOnWindowFocus: false,
    },
  },
})
```

### API Client Structure

```typescript
// Centralized API client with error handling
const api = {
  get: <T>(endpoint: string) => apiRequest<T>(endpoint),
  post: <T>(endpoint: string, data?: any) => apiRequest<T>(endpoint, { method: 'POST', body: JSON.stringify(data) }),
  // ... other methods
}
```

### Error Handling

- **Network Errors**: Automatic retry with exponential backoff
- **4xx Errors**: No retry (client errors)
- **Loading States**: Skeleton components and spinners
- **Error Boundaries**: Graceful error recovery

## 📱 Component Updates

### Before (Static Data)
```typescript
// Old approach
const services = getServices() // Static JSON
```

### After (Dynamic API)
```typescript
// New approach with TanStack Query
const { data: servicesData, isLoading, error } = useActiveServices()
const services = servicesData?.services || []

// Loading state
if (isLoading) return <ServiceGridSkeleton />

// Error state  
if (error) return <RetryWrapper error={error} onRetry={refetch} />
```

## 🎯 Benefits

### **For Users**
- ✅ **Real-time Content**: Always up-to-date information
- ✅ **Better Performance**: Intelligent caching and background updates
- ✅ **Offline Support**: Cached content available offline
- ✅ **Smooth UX**: Loading states and error recovery

### **For Administrators**
- ✅ **Easy Management**: Update content through CMS dashboard
- ✅ **No Deployments**: Content changes without code deployments
- ✅ **Rich Editor**: Professional content editing tools
- ✅ **Media Management**: Cloudinary integration for images

## 🔍 API Endpoints Used

```
GET /api/blogs              # Blog posts with pagination
GET /api/blogs/slug/{slug}  # Individual blog post
GET /api/services           # Services catalog
GET /api/packages           # Service packages
GET /api/testimonials       # Client testimonials
GET /api/gallery            # Portfolio gallery
GET /api/settings           # Site configuration
```

## 🛠️ Development Tools

### TanStack Query DevTools
- **Enabled in development**: View query states, cache, and network requests
- **Access**: Available in browser dev tools

### Error Monitoring
- **Development**: Detailed error messages and stack traces
- **Production**: User-friendly error messages with retry options

## 🚨 Troubleshooting

### Common Issues

**1. API Connection Failed**
```bash
# Check if CMS is running
curl http://localhost:3001/api/blogs

# Verify environment variables
echo $NEXT_PUBLIC_CMS_API_URL
```

**2. CORS Issues**
- Ensure CMS allows frontend origin
- Check network tab for CORS errors

**3. Data Not Loading**
- Verify CMS database is seeded
- Check API endpoints return data
- Review browser console for errors

### Debug Mode

Enable detailed logging:
```typescript
// In development
if (process.env.NODE_ENV === 'development') {
  console.log('API Response:', data)
  console.log('Query State:', { isLoading, error })
}
```

## 📈 Performance Optimization

### Caching Strategy
- **Stale Time**: 1-5 minutes for dynamic content
- **Cache Time**: 10-30 minutes for static content
- **Background Refetch**: Automatic updates when data is stale

### Bundle Size
- **Tree Shaking**: Only import used TanStack Query features
- **Code Splitting**: Lazy load API hooks when needed

## 🔮 Future Enhancements

- [ ] **Offline Support**: Service worker for offline content
- [ ] **Real-time Updates**: WebSocket integration for live updates
- [ ] **Advanced Caching**: Redis integration for better performance
- [ ] **Analytics**: Track content performance and user engagement

## 📚 Resources

- [TanStack Query Documentation](https://tanstack.com/query/latest)
- [Next.js API Routes](https://nextjs.org/docs/api-routes/introduction)
- [Error Boundary Patterns](https://react.dev/reference/react/Component#catching-rendering-errors-with-an-error-boundary)

---

**🎉 Your frontend is now fully integrated with the CMS!** 

Content updates in the CMS dashboard will automatically appear on the frontend without any code changes or deployments.
