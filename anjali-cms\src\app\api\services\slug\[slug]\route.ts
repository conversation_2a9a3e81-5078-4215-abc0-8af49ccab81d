import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { corsResponse, corsOptionsResponse } from '@/lib/cors'

export async function OPTIONS() {
  return corsOptionsResponse()
}

export async function GET(
  request: NextRequest,
  { params }: { params: { slug: string } }
) {
  try {
    // Public endpoint - no authentication required
    const service = await prisma.service.findUnique({
      where: { 
        slug: params.slug,
        status: 'ACTIVE' // Only return active services
      }
    })

    if (!service) {
      return corsResponse({ error: 'Service not found' }, { status: 404 })
    }

    return corsResponse(service)
  } catch (error) {
    console.error('Error fetching service by slug:', error)
    return corsResponse({ error: 'Internal server error' }, { status: 500 })
  }
}
