import { api, buildQueryParams } from '@/lib/api-client'
import type {
  Blog,
  BlogsResponse,
  BlogQueryParams,
  Service,
  ServicesResponse,
  ServiceQueryParams,
  Package,
  PackagesResponse,
  PackageQueryParams,
  Testimonial,
  TestimonialsResponse,
  TestimonialQueryParams,
  GalleryItem,
  GalleryResponse,
  GalleryQueryParams,
  SiteSetting,
  SettingsResponse,
} from '@/types/api'

// Blog API functions
export const blogApi = {
  getBlogs: async (params: BlogQueryParams = {}): Promise<BlogsResponse> => {
    const queryString = buildQueryParams(params)
    return api.get<BlogsResponse>(`/blogs${queryString}`)
  },

  getBlog: async (id: string): Promise<Blog> => {
    return api.get<Blog>(`/blogs/${id}`)
  },

  getBlogBySlug: async (slug: string): Promise<Blog> => {
    return api.get<Blog>(`/blogs/slug/${slug}`)
  },

  getFeaturedBlogs: async (limit = 3): Promise<BlogsResponse> => {
    return api.get<BlogsResponse>(`/blogs?featured=true&limit=${limit}&status=PUBLISHED`)
  },

  getRecentBlogs: async (limit = 5): Promise<BlogsResponse> => {
    return api.get<BlogsResponse>(`/blogs?limit=${limit}&status=PUBLISHED`)
  },
}

// Service API functions
export const serviceApi = {
  getServices: async (params: ServiceQueryParams = {}): Promise<ServicesResponse> => {
    const queryString = buildQueryParams(params)
    return api.get<ServicesResponse>(`/services${queryString}`)
  },

  getService: async (id: string): Promise<Service> => {
    return api.get<Service>(`/services/${id}`)
  },

  getServiceBySlug: async (slug: string): Promise<Service> => {
    return api.get<Service>(`/services/slug/${slug}`)
  },

  getActiveServices: async (): Promise<ServicesResponse> => {
    return api.get<ServicesResponse>('/services?status=ACTIVE')
  },

  getPopularServices: async (limit = 6): Promise<ServicesResponse> => {
    return api.get<ServicesResponse>(`/services?popular=true&status=ACTIVE&limit=${limit}`)
  },

  getServicesByCategory: async (category: string): Promise<ServicesResponse> => {
    return api.get<ServicesResponse>(`/services?category=${encodeURIComponent(category)}&status=ACTIVE`)
  },
}

// Package API functions
export const packageApi = {
  getPackages: async (params: PackageQueryParams = {}): Promise<PackagesResponse> => {
    const queryString = buildQueryParams(params)
    return api.get<PackagesResponse>(`/packages${queryString}`)
  },

  getPackage: async (id: string): Promise<Package> => {
    return api.get<Package>(`/packages/${id}`)
  },

  getPackageBySlug: async (slug: string): Promise<Package> => {
    return api.get<Package>(`/packages/slug/${slug}`)
  },

  getActivePackages: async (): Promise<PackagesResponse> => {
    return api.get<PackagesResponse>('/packages?status=ACTIVE')
  },

  getPopularPackages: async (limit = 3): Promise<PackagesResponse> => {
    return api.get<PackagesResponse>(`/packages?popular=true&status=ACTIVE&limit=${limit}`)
  },

  getFeaturedPackages: async (): Promise<PackagesResponse> => {
    return api.get<PackagesResponse>('/packages?popular=true&status=ACTIVE')
  },
}

// Testimonial API functions
export const testimonialApi = {
  getTestimonials: async (params: TestimonialQueryParams = {}): Promise<TestimonialsResponse> => {
    const queryString = buildQueryParams(params)
    return api.get<TestimonialsResponse>(`/testimonials${queryString}`)
  },

  getTestimonial: async (id: string): Promise<Testimonial> => {
    return api.get<Testimonial>(`/testimonials/${id}`)
  },

  getApprovedTestimonials: async (limit?: number): Promise<TestimonialsResponse> => {
    const params = { status: 'APPROVED', ...(limit && { limit }) }
    const queryString = buildQueryParams(params)
    return api.get<TestimonialsResponse>(`/testimonials${queryString}`)
  },

  getTestimonialsByService: async (service: string): Promise<TestimonialsResponse> => {
    return api.get<TestimonialsResponse>(`/testimonials?service=${encodeURIComponent(service)}&status=APPROVED`)
  },

  createTestimonial: async (data: Omit<Testimonial, 'id' | 'status' | 'createdAt' | 'updatedAt'>): Promise<Testimonial> => {
    return api.post<Testimonial>('/testimonials', data)
  },
}

// Gallery API functions
export const galleryApi = {
  getGallery: async (params: GalleryQueryParams = {}): Promise<GalleryResponse> => {
    const queryString = buildQueryParams(params)
    return api.get<GalleryResponse>(`/gallery${queryString}`)
  },

  getGalleryItem: async (id: string): Promise<GalleryItem> => {
    return api.get<GalleryItem>(`/gallery/${id}`)
  },

  getActiveGallery: async (limit?: number): Promise<GalleryResponse> => {
    const params = { status: 'ACTIVE', ...(limit && { limit }) }
    const queryString = buildQueryParams(params)
    return api.get<GalleryResponse>(`/gallery${queryString}`)
  },

  getFeaturedGallery: async (limit = 12): Promise<GalleryResponse> => {
    return api.get<GalleryResponse>(`/gallery?featured=true&status=ACTIVE&limit=${limit}`)
  },

  getGalleryByCategory: async (category: string): Promise<GalleryResponse> => {
    return api.get<GalleryResponse>(`/gallery?category=${encodeURIComponent(category)}&status=ACTIVE`)
  },
}

// Settings API functions
export const settingsApi = {
  getSettings: async (): Promise<SettingsResponse> => {
    return api.get<SettingsResponse>('/settings')
  },

  getSetting: async (key: string): Promise<SiteSetting> => {
    return api.get<SiteSetting>(`/settings/${key}`)
  },

  getSettingValue: async (key: string): Promise<string> => {
    const setting = await api.get<SiteSetting>(`/settings/${key}`)
    return setting.value
  },

  // Helper functions for common settings
  getSiteInfo: async () => {
    const settings = await settingsApi.getSettings()
    const siteSettings = settings.grouped?.site || []
    
    return {
      name: siteSettings.find(s => s.key === 'site.name')?.value || 'Anjali Makeup Artist',
      tagline: siteSettings.find(s => s.key === 'site.tagline')?.value || 'Enhancing Natural Beauty',
      description: siteSettings.find(s => s.key === 'site.description')?.value || '',
      logo: siteSettings.find(s => s.key === 'site.logo')?.value,
      favicon: siteSettings.find(s => s.key === 'site.favicon')?.value,
    }
  },

  getContactInfo: async () => {
    const settings = await settingsApi.getSettings()
    const contactSettings = settings.grouped?.contact || []
    
    return {
      email: contactSettings.find(s => s.key === 'contact.email')?.value,
      phone: contactSettings.find(s => s.key === 'contact.phone')?.value,
      address: contactSettings.find(s => s.key === 'contact.address')?.value,
      city: contactSettings.find(s => s.key === 'contact.city')?.value,
      state: contactSettings.find(s => s.key === 'contact.state')?.value,
      zipcode: contactSettings.find(s => s.key === 'contact.zipcode')?.value,
      country: contactSettings.find(s => s.key === 'contact.country')?.value,
    }
  },

  getSocialMedia: async () => {
    const settings = await settingsApi.getSettings()
    const socialSettings = settings.grouped?.social || []
    
    return {
      facebook: socialSettings.find(s => s.key === 'social.facebook')?.value,
      instagram: socialSettings.find(s => s.key === 'social.instagram')?.value,
      twitter: socialSettings.find(s => s.key === 'social.twitter')?.value,
      youtube: socialSettings.find(s => s.key === 'social.youtube')?.value,
      linkedin: socialSettings.find(s => s.key === 'social.linkedin')?.value,
      tiktok: socialSettings.find(s => s.key === 'social.tiktok')?.value,
    }
  },

  getSEODefaults: async () => {
    const settings = await settingsApi.getSettings()
    const seoSettings = settings.grouped?.seo || []
    
    return {
      metaTitle: seoSettings.find(s => s.key === 'seo.meta_title')?.value,
      metaDescription: seoSettings.find(s => s.key === 'seo.meta_description')?.value,
      keywords: seoSettings.find(s => s.key === 'seo.keywords')?.value,
      ogImage: seoSettings.find(s => s.key === 'seo.og_image')?.value,
    }
  },

  getBusinessInfo: async () => {
    const settings = await settingsApi.getSettings()
    const businessSettings = settings.grouped?.business || []
    
    return {
      hours: businessSettings.find(s => s.key === 'business.hours')?.value,
      servicesIntro: businessSettings.find(s => s.key === 'business.services_intro')?.value,
      aboutText: businessSettings.find(s => s.key === 'business.about_text')?.value,
      bookingUrl: businessSettings.find(s => s.key === 'business.booking_url')?.value,
    }
  },
}

// Combined API object
export const cmsApi = {
  blogs: blogApi,
  services: serviceApi,
  packages: packageApi,
  testimonials: testimonialApi,
  gallery: galleryApi,
  settings: settingsApi,
}
