'use client'

import { useState } from 'react'
import { <PERSON>ert<PERSON>ircle, RefreshCw, Wifi, WifiOff } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'

interface RetryWrapperProps {
  children: React.ReactNode
  error?: Error | null
  isLoading?: boolean
  onRetry: () => void
  title?: string
  description?: string
  showNetworkStatus?: boolean
}

export function RetryWrapper({
  children,
  error,
  isLoading,
  onRetry,
  title = "Something went wrong",
  description = "We're having trouble loading this content. Please try again.",
  showNetworkStatus = true
}: RetryWrapperProps) {
  const [isOnline, setIsOnline] = useState(navigator.onLine)

  // Monitor network status
  if (typeof window !== 'undefined') {
    window.addEventListener('online', () => setIsOnline(true))
    window.addEventListener('offline', () => setIsOnline(false))
  }

  if (error) {
    const isNetworkError = error.message.includes('Network') || error.message.includes('fetch')
    
    return (
      <div className="min-h-[300px] flex items-center justify-center p-6">
        <Card className="w-full max-w-md">
          <CardHeader className="text-center">
            <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
              {isNetworkError ? (
                <WifiOff className="w-8 h-8 text-red-600" />
              ) : (
                <AlertCircle className="w-8 h-8 text-red-600" />
              )}
            </div>
            <CardTitle className="text-xl">{title}</CardTitle>
            <CardDescription>
              {isNetworkError ? 
                "Please check your internet connection and try again." : 
                description
              }
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {showNetworkStatus && (
              <div className="flex items-center justify-center gap-2 text-sm">
                {isOnline ? (
                  <>
                    <Wifi className="w-4 h-4 text-green-600" />
                    <span className="text-green-600">Connected</span>
                  </>
                ) : (
                  <>
                    <WifiOff className="w-4 h-4 text-red-600" />
                    <span className="text-red-600">No connection</span>
                  </>
                )}
              </div>
            )}
            
            {process.env.NODE_ENV === 'development' && error && (
              <div className="p-3 bg-red-50 border border-red-200 rounded-md">
                <p className="text-sm text-red-800 font-mono break-all">
                  {error.message}
                </p>
              </div>
            )}
            
            <div className="flex gap-2">
              <Button 
                onClick={onRetry} 
                disabled={isLoading}
                className="flex-1"
              >
                {isLoading ? (
                  <>
                    <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                    Retrying...
                  </>
                ) : (
                  <>
                    <RefreshCw className="w-4 h-4 mr-2" />
                    Try Again
                  </>
                )}
              </Button>
              <Button 
                variant="outline" 
                onClick={() => window.location.reload()}
                className="flex-1"
              >
                Reload Page
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  return <>{children}</>
}

// Hook for retry logic
export function useRetry(queryFn: () => void, maxRetries = 3) {
  const [retryCount, setRetryCount] = useState(0)
  const [isRetrying, setIsRetrying] = useState(false)

  const retry = async () => {
    if (retryCount >= maxRetries) {
      return
    }

    setIsRetrying(true)
    setRetryCount(prev => prev + 1)

    try {
      await queryFn()
    } catch (error) {
      console.error(`Retry ${retryCount + 1} failed:`, error)
    } finally {
      setIsRetrying(false)
    }
  }

  const reset = () => {
    setRetryCount(0)
    setIsRetrying(false)
  }

  return {
    retry,
    reset,
    retryCount,
    isRetrying,
    canRetry: retryCount < maxRetries
  }
}

// Network status hook
export function useNetworkStatus() {
  const [isOnline, setIsOnline] = useState(
    typeof navigator !== 'undefined' ? navigator.onLine : true
  )

  if (typeof window !== 'undefined') {
    window.addEventListener('online', () => setIsOnline(true))
    window.addEventListener('offline', () => setIsOnline(false))
  }

  return isOnline
}
