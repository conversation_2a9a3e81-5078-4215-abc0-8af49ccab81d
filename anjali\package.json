{"name": "<PERSON><PERSON><PERSON>", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build && next-sitemap", "start": "next start", "lint": "next lint", "postbuild": "next-sitemap"}, "dependencies": {"@emailjs/browser": "^4.4.1", "@hookform/resolvers": "^5.1.1", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-toast": "^1.2.14", "@tanstack/react-query": "^5.83.0", "@tanstack/react-query-devtools": "^5.83.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "framer-motion": "^12.23.6", "gray-matter": "^4.0.3", "lucide-react": "^0.525.0", "next": "15.4.1", "next-seo": "^6.8.0", "next-sitemap": "^4.2.3", "react": "19.1.0", "react-dom": "19.1.0", "react-hook-form": "^7.60.0", "remark": "^15.0.1", "remark-html": "^16.0.1", "tailwind-merge": "^3.3.1", "zod": "^4.0.5"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20.19.8", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.4.1", "tailwindcss": "^4", "typescript": "^5"}}