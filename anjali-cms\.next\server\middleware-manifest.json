{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_82ad035c._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_fb905aa2.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/api(?:\\/((?:[^\\/#\\?]+?)(?:\\/(?:[^\\/#\\?]+?))*))?(\\\\.json)?[\\/#\\?]?$", "originalSource": "/api/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "qqs/51Z3btC6w/UJ2TiHtxQZT70yB58AMKU3p+RYPQs=", "__NEXT_PREVIEW_MODE_ID": "ceaa09b73bb905da2cb1f99b4082f84a", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "961c7be4afae9abed404d073f997c96b8ad318f22754b9dc422259307153f039", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "39978982ee94736542482a47a3b7ecd14dde1e2dc6413d2889c78e06041a6ec0"}}}, "sortedMiddleware": ["/"], "functions": {}}