'use client'

import Script from 'next/script'
import { getSiteConfig } from '@/lib/data'

export default function Analytics() {
  const siteConfig = getSiteConfig()
  const gaId = siteConfig.analytics.googleAnalyticsId

  // Only load analytics in production
  if (process.env.NODE_ENV !== 'production' || !gaId || gaId === 'G-XXXXXXXXXX') {
    return null
  }

  return (
    <>
      <Script
        src={`https://www.googletagmanager.com/gtag/js?id=${gaId}`}
        strategy="afterInteractive"
      />
      <Script id="google-analytics" strategy="afterInteractive">
        {`
          window.dataLayer = window.dataLayer || [];
          function gtag(){dataLayer.push(arguments);}
          gtag('js', new Date());
          gtag('config', '${gaId}', {
            page_title: document.title,
            page_location: window.location.href,
          });
        `}
      </Script>
    </>
  )
}
