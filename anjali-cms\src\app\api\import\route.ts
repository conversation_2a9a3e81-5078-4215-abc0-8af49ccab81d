import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { generateSlug } from '@/lib/utils'

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const { type, data } = body

    if (!type || !data) {
      return NextResponse.json({ error: 'Type and data are required' }, { status: 400 })
    }

    let result
    let message

    switch (type) {
      case 'blogs':
        result = await importBlogs(data)
        message = `Imported ${result.count} blog posts`
        break

      case 'services':
        result = await importServices(data)
        message = `Imported ${result.count} services`
        break

      case 'testimonials':
        result = await importTestimonials(data)
        message = `Imported ${result.count} testimonials`
        break

      case 'gallery':
        result = await importGallery(data)
        message = `Imported ${result.count} gallery items`
        break

      case 'packages':
        result = await importPackages(data)
        message = `Imported ${result.count} packages`
        break

      default:
        return NextResponse.json({ error: 'Invalid import type' }, { status: 400 })
    }

    return NextResponse.json({
      message,
      imported: result.count,
      errors: result.errors || []
    })
  } catch (error) {
    console.error('Import error:', error)
    return NextResponse.json({ error: 'Import failed' }, { status: 500 })
  }
}

async function importBlogs(blogs: any[]) {
  const errors: string[] = []
  let count = 0

  for (const blog of blogs) {
    try {
      const slug = generateSlug(blog.title || `blog-${Date.now()}`)
      
      // Check if blog already exists
      const existing = await prisma.blog.findUnique({ where: { slug } })
      if (existing) {
        errors.push(`Blog "${blog.title}" already exists`)
        continue
      }

      await prisma.blog.create({
        data: {
          title: blog.title || 'Untitled',
          slug,
          excerpt: blog.excerpt || blog.description,
          content: blog.content || blog.description || '',
          author: blog.author || 'Admin',
          status: 'PUBLISHED',
          featured: blog.featured || false,
          image: blog.image,
          readTime: blog.readTime,
          publishedAt: blog.date ? new Date(blog.date) : new Date(),
        }
      })
      count++
    } catch (error) {
      errors.push(`Failed to import blog "${blog.title}": ${error}`)
    }
  }

  return { count, errors }
}

async function importServices(services: any[]) {
  const errors: string[] = []
  let count = 0

  for (const service of services) {
    try {
      const slug = generateSlug(service.title || service.name || `service-${Date.now()}`)
      
      // Check if service already exists
      const existing = await prisma.service.findUnique({ where: { slug } })
      if (existing) {
        errors.push(`Service "${service.title || service.name}" already exists`)
        continue
      }

      await prisma.service.create({
        data: {
          title: service.title || service.name || 'Untitled Service',
          slug,
          description: service.description || '',
          features: service.features || [],
          duration: service.duration,
          price: service.price,
          image: service.image,
          category: service.category,
          popular: service.popular || false,
          status: 'ACTIVE',
        }
      })
      count++
    } catch (error) {
      errors.push(`Failed to import service "${service.title || service.name}": ${error}`)
    }
  }

  return { count, errors }
}

async function importTestimonials(testimonials: any[]) {
  const errors: string[] = []
  let count = 0

  for (const testimonial of testimonials) {
    try {
      await prisma.testimonial.create({
        data: {
          name: testimonial.name || 'Anonymous',
          email: testimonial.email,
          message: testimonial.message || testimonial.review || '',
          rating: testimonial.rating || 5,
          image: testimonial.image || testimonial.avatar,
          service: testimonial.service,
          status: 'APPROVED',
        }
      })
      count++
    } catch (error) {
      errors.push(`Failed to import testimonial from "${testimonial.name}": ${error}`)
    }
  }

  return { count, errors }
}

async function importGallery(galleryItems: any[]) {
  const errors: string[] = []
  let count = 0

  for (const item of galleryItems) {
    try {
      if (!item.image) {
        errors.push(`Gallery item missing image: ${item.title || 'Untitled'}`)
        continue
      }

      await prisma.gallery.create({
        data: {
          title: item.title || item.name || 'Untitled',
          description: item.description,
          image: item.image,
          category: item.category,
          tags: item.tags || [],
          featured: item.featured || false,
          status: 'ACTIVE',
        }
      })
      count++
    } catch (error) {
      errors.push(`Failed to import gallery item "${item.title || item.name}": ${error}`)
    }
  }

  return { count, errors }
}

async function importPackages(packages: any[]) {
  const errors: string[] = []
  let count = 0

  for (const pkg of packages) {
    try {
      const slug = generateSlug(pkg.name || pkg.title || `package-${Date.now()}`)

      // Check if package already exists
      const existing = await prisma.package.findUnique({ where: { slug } })
      if (existing) {
        errors.push(`Package "${pkg.name || pkg.title}" already exists`)
        continue
      }

      await prisma.package.create({
        data: {
          name: pkg.name || pkg.title || 'Untitled Package',
          slug,
          description: pkg.description || '',
          services: pkg.services || [],
          features: pkg.features || [],
          price: pkg.price || 'Contact for pricing',
          originalPrice: pkg.originalPrice,
          duration: pkg.duration,
          popular: pkg.popular || false,
          image: pkg.image,
          category: pkg.category,
          status: 'ACTIVE',
        }
      })
      count++
    } catch (error) {
      errors.push(`Failed to import package "${pkg.name || pkg.title}": ${error}`)
    }
  }

  return { count, errors }
}
