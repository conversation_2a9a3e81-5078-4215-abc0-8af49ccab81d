'use client'

import { Package, Sparkles, Percent } from 'lucide-react'
import { Badge } from '@/components/ui/badge'
import { Section } from '@/components/ui/section'
import { AnimatedElement } from '@/components/ui/animated-element'

export default function PackagesHero() {
  return (
    <Section className="pt-24 pb-16 bg-gradient-to-br from-cream via-white to-blush-pink-light">
      <div className="text-center space-y-8">
        <AnimatedElement animation="slideUp">
          <Badge variant="secondary" className="text-sm px-4 py-2 mb-4">
            <Package className="w-4 h-4 mr-2" />
            Special Packages & Deals
          </Badge>
          
          <h1 className="font-display text-4xl md:text-5xl lg:text-6xl font-bold text-text-primary leading-tight">
            Save More with Our
            <span className="block text-transparent bg-gradient-to-r from-rose-gold to-blush-pink bg-clip-text">
              Curated Packages
            </span>
          </h1>
          
          <p className="text-xl text-text-secondary leading-relaxed max-w-3xl mx-auto">
            Get the best value with our carefully designed packages that combine multiple 
            services for your special occasions. Perfect for brides, events, and regular 
            beauty maintenance.
          </p>
        </AnimatedElement>

        {/* Package Benefits */}
        <AnimatedElement animation="slideUp" delay={0.3}>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-2xl mx-auto">
            <div className="text-center">
              <div className="w-16 h-16 bg-gradient-to-br from-rose-gold to-blush-pink rounded-full flex items-center justify-center mx-auto mb-3">
                <Percent className="w-8 h-8 text-white" />
              </div>
              <div className="text-2xl font-bold text-text-primary mb-1">Up to 25%</div>
              <div className="text-text-secondary">Savings</div>
            </div>
            
            <div className="text-center">
              <div className="w-16 h-16 bg-gradient-to-br from-blush-pink to-lavender rounded-full flex items-center justify-center mx-auto mb-3">
                <Sparkles className="w-8 h-8 text-white" />
              </div>
              <div className="text-2xl font-bold text-text-primary mb-1">5</div>
              <div className="text-text-secondary">Package Options</div>
            </div>
            
            <div className="text-center">
              <div className="w-16 h-16 bg-gradient-to-br from-lavender to-rose-gold rounded-full flex items-center justify-center mx-auto mb-3">
                <Package className="w-8 h-8 text-white" />
              </div>
              <div className="text-2xl font-bold text-text-primary mb-1">Complete</div>
              <div className="text-text-secondary">Solutions</div>
            </div>
          </div>
        </AnimatedElement>

        {/* Package Types */}
        <AnimatedElement animation="slideUp" delay={0.5}>
          <div className="flex flex-wrap justify-center gap-3 max-w-2xl mx-auto">
            {['Bridal Complete', 'Party Glam', 'Engagement Special', 'Photoshoot Pro', 'Monthly Beauty'].map((packageType) => (
              <Badge key={packageType} variant="outline" className="text-sm px-4 py-2">
                {packageType}
              </Badge>
            ))}
          </div>
        </AnimatedElement>
      </div>
    </Section>
  )
}
