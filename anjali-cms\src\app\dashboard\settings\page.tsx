"use client"

import { useState, useEffect } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { toast } from 'sonner'
import { Save, Globe, Mail, Phone, MapPin, Facebook, Instagram, Twitter } from 'lucide-react'

const settingsSchema = z.object({
  // Site Information
  'site.name': z.string().min(1, 'Site name is required'),
  'site.tagline': z.string().optional(),
  'site.description': z.string().optional(),
  'site.logo': z.string().optional(),
  'site.favicon': z.string().optional(),
  
  // Contact Information
  'contact.email': z.string().email('Valid email is required').optional().or(z.literal('')),
  'contact.phone': z.string().optional(),
  'contact.address': z.string().optional(),
  'contact.city': z.string().optional(),
  'contact.state': z.string().optional(),
  'contact.zipcode': z.string().optional(),
  'contact.country': z.string().optional(),
  
  // Social Media
  'social.facebook': z.string().optional(),
  'social.instagram': z.string().optional(),
  'social.twitter': z.string().optional(),
  'social.youtube': z.string().optional(),
  'social.linkedin': z.string().optional(),
  'social.tiktok': z.string().optional(),
  
  // SEO Defaults
  'seo.meta_title': z.string().optional(),
  'seo.meta_description': z.string().optional(),
  'seo.keywords': z.string().optional(),
  'seo.og_image': z.string().optional(),
  
  // Business Settings
  'business.hours': z.string().optional(),
  'business.services_intro': z.string().optional(),
  'business.about_text': z.string().optional(),
  'business.booking_url': z.string().optional(),
})

type SettingsFormData = z.infer<typeof settingsSchema>

interface Setting {
  key: string
  value: string
  type: string
  description?: string
}

export default function SettingsPage() {
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [settings, setSettings] = useState<Setting[]>([])

  const form = useForm<SettingsFormData>({
    resolver: zodResolver(settingsSchema),
    defaultValues: {},
  })

  const fetchSettings = async () => {
    try {
      const response = await fetch('/api/settings')
      if (response.ok) {
        const data = await response.json()
        setSettings(data.settings)
        
        // Convert settings array to form values
        const formValues: any = {}
        data.settings.forEach((setting: Setting) => {
          formValues[setting.key] = setting.value
        })
        
        form.reset(formValues)
      } else {
        toast.error('Failed to fetch settings')
      }
    } catch (error) {
      toast.error('Error fetching settings')
    } finally {
      setLoading(false)
    }
  }

  const onSubmit = async (data: SettingsFormData) => {
    setSaving(true)

    try {
      // Convert form data to settings array
      const settingsArray = Object.entries(data).map(([key, value]) => ({
        key,
        value: value || '',
        type: getFieldType(key),
        description: getFieldDescription(key),
      }))

      const response = await fetch('/api/settings', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(settingsArray),
      })

      if (response.ok) {
        toast.success('Settings saved successfully')
        fetchSettings()
      } else {
        toast.error('Failed to save settings')
      }
    } catch (error) {
      toast.error('Error saving settings')
    } finally {
      setSaving(false)
    }
  }

  const getFieldType = (key: string): string => {
    if (key.includes('email')) return 'EMAIL'
    if (key.includes('url') || key.includes('facebook') || key.includes('instagram') || key.includes('twitter')) return 'URL'
    return 'TEXT'
  }

  const getFieldDescription = (key: string): string => {
    const descriptions: Record<string, string> = {
      'site.name': 'The name of your makeup artistry business',
      'site.tagline': 'A short tagline or slogan for your business',
      'site.description': 'Brief description of your services and expertise',
      'contact.email': 'Primary contact email for inquiries',
      'contact.phone': 'Phone number for bookings and consultations',
      'contact.address': 'Business address or service area',
      'social.facebook': 'Facebook page URL',
      'social.instagram': 'Instagram profile URL',
      'social.twitter': 'Twitter profile URL',
      'seo.meta_title': 'Default page title for SEO',
      'seo.meta_description': 'Default meta description for search engines',
      'business.booking_url': 'Link to your booking system or calendar',
    }
    return descriptions[key] || ''
  }

  useEffect(() => {
    fetchSettings()
  }, [])

  if (loading) {
    return <div>Loading...</div>
  }

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold">Site Settings</h1>
        <p className="text-muted-foreground">
          Configure your website settings and business information
        </p>
      </div>

      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          <Tabs defaultValue="general" className="space-y-6">
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="general">General</TabsTrigger>
              <TabsTrigger value="contact">Contact</TabsTrigger>
              <TabsTrigger value="social">Social Media</TabsTrigger>
              <TabsTrigger value="seo">SEO & Business</TabsTrigger>
            </TabsList>

            <TabsContent value="general" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Globe className="h-5 w-5" />
                    Site Information
                  </CardTitle>
                  <CardDescription>
                    Basic information about your makeup artistry website
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <FormField
                    control={form.control}
                    name="site.name"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Site Name</FormLabel>
                        <FormControl>
                          <Input placeholder="Anjali Makeup Artist" {...field} />
                        </FormControl>
                        <FormDescription>
                          The name of your makeup artistry business
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="site.tagline"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Tagline</FormLabel>
                        <FormControl>
                          <Input placeholder="Enhancing Natural Beauty" {...field} />
                        </FormControl>
                        <FormDescription>
                          A short tagline or slogan for your business
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="site.description"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Site Description</FormLabel>
                        <FormControl>
                          <Textarea
                            placeholder="Professional makeup artist specializing in bridal, party, and special occasion makeup..."
                            className="min-h-[100px]"
                            {...field}
                          />
                        </FormControl>
                        <FormDescription>
                          Brief description of your services and expertise
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="contact" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Phone className="h-5 w-5" />
                    Contact Information
                  </CardTitle>
                  <CardDescription>
                    How clients can reach you for bookings and inquiries
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <FormField
                      control={form.control}
                      name="contact.email"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="flex items-center gap-2">
                            <Mail className="h-4 w-4" />
                            Email
                          </FormLabel>
                          <FormControl>
                            <Input type="email" placeholder="<EMAIL>" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="contact.phone"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="flex items-center gap-2">
                            <Phone className="h-4 w-4" />
                            Phone
                          </FormLabel>
                          <FormControl>
                            <Input placeholder="+977 98XXXXXXXX" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <FormField
                    control={form.control}
                    name="contact.address"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="flex items-center gap-2">
                          <MapPin className="h-4 w-4" />
                          Address
                        </FormLabel>
                        <FormControl>
                          <Input placeholder="Street address" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <FormField
                      control={form.control}
                      name="contact.city"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>City</FormLabel>
                          <FormControl>
                            <Input placeholder="Kathmandu" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="contact.state"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>State/Province</FormLabel>
                          <FormControl>
                            <Input placeholder="Bagmati" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="contact.zipcode"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>ZIP Code</FormLabel>
                          <FormControl>
                            <Input placeholder="44600" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <FormField
                    control={form.control}
                    name="contact.country"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Country</FormLabel>
                        <FormControl>
                          <Input placeholder="Nepal" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="social" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Instagram className="h-5 w-5" />
                    Social Media Links
                  </CardTitle>
                  <CardDescription>
                    Connect your social media profiles to showcase your work
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <FormField
                    control={form.control}
                    name="social.instagram"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="flex items-center gap-2">
                          <Instagram className="h-4 w-4" />
                          Instagram
                        </FormLabel>
                        <FormControl>
                          <Input placeholder="https://instagram.com/yourusername" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="social.facebook"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="flex items-center gap-2">
                          <Facebook className="h-4 w-4" />
                          Facebook
                        </FormLabel>
                        <FormControl>
                          <Input placeholder="https://facebook.com/yourpage" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="social.twitter"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="flex items-center gap-2">
                          <Twitter className="h-4 w-4" />
                          Twitter
                        </FormLabel>
                        <FormControl>
                          <Input placeholder="https://twitter.com/yourusername" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <FormField
                      control={form.control}
                      name="social.youtube"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>YouTube</FormLabel>
                          <FormControl>
                            <Input placeholder="https://youtube.com/yourchannel" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="social.tiktok"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>TikTok</FormLabel>
                          <FormControl>
                            <Input placeholder="https://tiktok.com/@yourusername" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="seo" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>SEO Defaults</CardTitle>
                  <CardDescription>
                    Default SEO settings for your website
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <FormField
                    control={form.control}
                    name="seo.meta_title"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Default Meta Title</FormLabel>
                        <FormControl>
                          <Input placeholder="Anjali Makeup Artist - Professional Makeup Services" {...field} />
                        </FormControl>
                        <FormDescription>
                          Default page title for SEO (used when pages don't have custom titles)
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="seo.meta_description"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Default Meta Description</FormLabel>
                        <FormControl>
                          <Textarea
                            placeholder="Professional makeup artist specializing in bridal, party, and special occasion makeup. Book your appointment today!"
                            {...field}
                          />
                        </FormControl>
                        <FormDescription>
                          Default meta description for search engines
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="seo.keywords"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Default Keywords</FormLabel>
                        <FormControl>
                          <Input placeholder="makeup artist, bridal makeup, party makeup, beauty services" {...field} />
                        </FormControl>
                        <FormDescription>
                          Comma-separated keywords for SEO
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Business Settings</CardTitle>
                  <CardDescription>
                    Additional business information and settings
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <FormField
                    control={form.control}
                    name="business.booking_url"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Booking URL</FormLabel>
                        <FormControl>
                          <Input placeholder="https://calendly.com/yourbooking" {...field} />
                        </FormControl>
                        <FormDescription>
                          Link to your booking system or calendar
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="business.hours"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Business Hours</FormLabel>
                        <FormControl>
                          <Textarea
                            placeholder="Monday - Friday: 9:00 AM - 6:00 PM&#10;Saturday: 10:00 AM - 4:00 PM&#10;Sunday: By appointment only"
                            {...field}
                          />
                        </FormControl>
                        <FormDescription>
                          Your business operating hours
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="business.about_text"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>About Text</FormLabel>
                        <FormControl>
                          <Textarea
                            placeholder="Brief introduction about yourself and your expertise..."
                            className="min-h-[100px]"
                            {...field}
                          />
                        </FormControl>
                        <FormDescription>
                          Text for your about section
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>

          <div className="flex justify-end">
            <Button type="submit" disabled={saving}>
              <Save className="h-4 w-4 mr-2" />
              {saving ? 'Saving...' : 'Save Settings'}
            </Button>
          </div>
        </form>
      </Form>
    </div>
  )
}
