'use client'

import { Metadata } from 'next'
import { notFound } from 'next/navigation'
import BlogPostContent from '@/components/sections/blog-post-content'
import { useBlogBySlug } from '@/hooks/use-api'
import { Loader2 } from 'lucide-react'

interface BlogPostPageProps {
  params: {
    slug: string
  }
}

export default function BlogPostPage({ params }: BlogPostPageProps) {
  const { data: post, isLoading, error } = useBlogBySlug(params.slug)

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="w-8 h-8 animate-spin text-rose-gold mx-auto mb-4" />
          <p className="text-text-secondary">Loading article...</p>
        </div>
      </div>
    )
  }

  if (error || !post) {
    notFound()
  }

  return <BlogPostContent post={post} />
}
