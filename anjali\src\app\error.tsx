'use client'

import { useEffect } from 'react'
import { RefreshCw, Home, AlertTriangle } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import Link from 'next/link'

export default function Error({
  error,
  reset,
}: {
  error: Error & { digest?: string }
  reset: () => void
}) {
  useEffect(() => {
    // Log the error to an error reporting service
    console.error('Application error:', error)
  }, [error])

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-cream via-white to-blush-pink-light px-4">
      <div className="text-center space-y-8 max-w-md mx-auto">
        {/* Error Icon */}
        <div className="w-24 h-24 bg-gradient-to-br from-red-400 to-red-600 rounded-full flex items-center justify-center mx-auto">
          <AlertTriangle className="w-12 h-12 text-white" />
        </div>

        {/* Error Message */}
        <div className="space-y-4">
          <h1 className="font-display text-3xl md:text-4xl font-bold text-text-primary">
            Something went wrong!
          </h1>
          <p className="text-text-secondary leading-relaxed">
            We're sorry, but something unexpected happened. Please try refreshing 
            the page or contact us if the problem persists.
          </p>
          
          {process.env.NODE_ENV === 'development' && (
            <Card className="bg-red-50 border-red-200">
              <CardContent className="p-4">
                <p className="text-red-800 text-sm font-mono">
                  {error.message}
                </p>
              </CardContent>
            </Card>
          )}
        </div>

        {/* Action Buttons */}
        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <Button 
            onClick={reset}
            variant="gradient" 
            className="group"
          >
            <RefreshCw className="w-4 h-4 mr-2" />
            Try Again
          </Button>
          
          <Button asChild variant="outline">
            <Link href="/">
              <Home className="w-4 h-4 mr-2" />
              Go Home
            </Link>
          </Button>
        </div>

        {/* Contact Information */}
        <Card className="bg-white/80 backdrop-blur-sm border-0">
          <CardContent className="p-6">
            <h3 className="font-semibold text-text-primary mb-4">
              Need Help?
            </h3>
            <p className="text-text-secondary text-sm mb-4">
              If this error continues, please contact us and we'll help resolve the issue.
            </p>
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span className="text-text-secondary">WhatsApp:</span>
                <span className="text-text-primary">+977-9800000000</span>
              </div>
              <div className="flex justify-between">
                <span className="text-text-secondary">Email:</span>
                <span className="text-text-primary"><EMAIL></span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
