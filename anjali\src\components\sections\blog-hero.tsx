'use client'

import { <PERSON><PERSON><PERSON>, Lightbulb, TrendingUp } from 'lucide-react'
import { Badge } from '@/components/ui/badge'
import { Section } from '@/components/ui/section'
import { AnimatedElement } from '@/components/ui/animated-element'

export default function BlogHero() {
  return (
    <Section className="pt-24 pb-16 bg-gradient-to-br from-cream via-white to-blush-pink-light">
      <div className="text-center space-y-8">
        <AnimatedElement animation="slideUp">
          <Badge variant="secondary" className="text-sm px-4 py-2 mb-4">
            <BookOpen className="w-4 h-4 mr-2" />
            Beauty Blog & Tips
          </Badge>
          
          <h1 className="font-display text-4xl md:text-5xl lg:text-6xl font-bold text-text-primary leading-tight">
            Expert Beauty
            <span className="block text-transparent bg-gradient-to-r from-rose-gold to-blush-pink bg-clip-text">
              Tips & Trends
            </span>
          </h1>
          
          <p className="text-xl text-text-secondary leading-relaxed max-w-3xl mx-auto">
            Discover professional makeup tips, beauty trends, and expert advice from our 
            experienced makeup artist. Learn techniques to enhance your natural beauty 
            and stay updated with the latest in the beauty world.
          </p>
        </AnimatedElement>

        {/* Blog Stats */}
        <AnimatedElement animation="slideUp" delay={0.3}>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-2xl mx-auto">
            <div className="text-center">
              <div className="w-16 h-16 bg-gradient-to-br from-rose-gold to-blush-pink rounded-full flex items-center justify-center mx-auto mb-3">
                <BookOpen className="w-8 h-8 text-white" />
              </div>
              <div className="text-2xl font-bold text-text-primary mb-1">5+</div>
              <div className="text-text-secondary">Blog Articles</div>
            </div>
            
            <div className="text-center">
              <div className="w-16 h-16 bg-gradient-to-br from-blush-pink to-lavender rounded-full flex items-center justify-center mx-auto mb-3">
                <Lightbulb className="w-8 h-8 text-white" />
              </div>
              <div className="text-2xl font-bold text-text-primary mb-1">Expert</div>
              <div className="text-text-secondary">Tips & Advice</div>
            </div>
            
            <div className="text-center">
              <div className="w-16 h-16 bg-gradient-to-br from-lavender to-rose-gold rounded-full flex items-center justify-center mx-auto mb-3">
                <TrendingUp className="w-8 h-8 text-white" />
              </div>
              <div className="text-2xl font-bold text-text-primary mb-1">Latest</div>
              <div className="text-text-secondary">Beauty Trends</div>
            </div>
          </div>
        </AnimatedElement>

        {/* Blog Categories */}
        <AnimatedElement animation="slideUp" delay={0.5}>
          <div className="flex flex-wrap justify-center gap-3 max-w-2xl mx-auto">
            {['Bridal Tips', 'Beauty Tips', 'Cultural Beauty', 'Seasonal Beauty', 'Trends'].map((category) => (
              <Badge key={category} variant="outline" className="text-sm px-4 py-2">
                {category}
              </Badge>
            ))}
          </div>
        </AnimatedElement>
      </div>
    </Section>
  )
}
