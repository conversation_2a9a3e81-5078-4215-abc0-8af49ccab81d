# Basic Cloudinary Upload Setup

## 🎯 Overview

I've created a basic Cloudinary image upload component that uses the standard Cloudinary REST API instead of the Next.js component. This gives you full control over the upload process.

## ✅ Features

### **Basic Cloudinary Upload Component**
- ✅ **File Selection**: Click to choose image files
- ✅ **Direct Upload**: Uploads directly to Cloudinary via REST API
- ✅ **Progress Bar**: Shows upload progress with percentage
- ✅ **Error Handling**: User-friendly error messages
- ✅ **File Validation**: Checks file type and size
- ✅ **Image Preview**: Shows uploaded image with remove option
- ✅ **No Dependencies**: Uses standard fetch API, no extra packages

## 🔧 Setup Instructions

### 1. **Create Cloudinary Upload Preset**

**Go to Cloudinary Dashboard:**
1. Visit [cloudinary.com](https://cloudinary.com) and login
2. Go to **Settings** → **Upload** → **Upload presets**
3. Click **"Add upload preset"**

**Configure the preset:**
```
Preset name: anjali_cms_unsigned
Signing mode: Unsigned ← VERY IMPORTANT!
Folder: anjali-cms
Resource type: Image
Access mode: Public
Allowed formats: jpg,jpeg,png,gif,webp
Max file size: 5000000 (5MB)
Unique filename: True
Use filename: True
```

**Save the preset**

### 2. **Environment Variables**

Add to your `.env.local` file:

```env
# Cloudinary Configuration
NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME="your-cloud-name"
NEXT_PUBLIC_CLOUDINARY_UPLOAD_PRESET="anjali_cms_unsigned"

# Optional (for advanced features)
CLOUDINARY_API_KEY="your-api-key"
CLOUDINARY_API_SECRET="your-api-secret"
```

**Find your credentials:**
- Go to **Dashboard** → **Account Details**
- Copy your **Cloud name**

### 3. **How It Works**

The component uploads files directly to Cloudinary using their REST API:

```typescript
const uploadToCloudinary = async (file: File) => {
  const formData = new FormData()
  formData.append('file', file)
  formData.append('upload_preset', 'anjali_cms_unsigned')
  formData.append('folder', 'anjali-cms')

  const response = await fetch(
    `https://api.cloudinary.com/v1_1/${cloudName}/image/upload`,
    {
      method: 'POST',
      body: formData,
    }
  )

  return response.json()
}
```

## 🎨 Component Features

### **User Experience**
- **Click to Upload**: Simple file selection
- **Progress Feedback**: Real-time upload progress
- **Image Preview**: Immediate preview of uploaded image
- **Error Messages**: Clear error descriptions
- **Remove Option**: Easy image removal

### **File Validation**
- **Supported formats**: JPG, PNG, GIF, WebP
- **Max file size**: 5MB
- **Type checking**: Validates MIME types
- **Size checking**: Prevents oversized uploads

### **Error Handling**
- **Network errors**: Connection issues
- **File validation**: Invalid file types/sizes
- **Cloudinary errors**: API-specific errors
- **Configuration errors**: Missing environment variables

## 🧪 Testing

### **Test Upload Process:**

1. **Select an image file** (JPG, PNG, GIF, WebP)
2. **Watch progress bar** during upload
3. **See image preview** when complete
4. **Check Cloudinary dashboard** for uploaded file

### **Test Error Scenarios:**

1. **Large file**: Try uploading >5MB file
2. **Wrong format**: Try uploading PDF or other non-image
3. **No internet**: Disconnect and try upload
4. **Wrong config**: Use invalid cloud name

## 🔍 Troubleshooting

### **Common Issues:**

#### **"Upload preset must be whitelisted"**
- ✅ Ensure preset signing mode is **"Unsigned"**
- ✅ Check preset name spelling: `anjali_cms_unsigned`
- ✅ Verify preset is saved in Cloudinary

#### **"Invalid cloud name"**
- ✅ Check `NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME` in `.env.local`
- ✅ Ensure it matches your Cloudinary account exactly
- ✅ No spaces or special characters

#### **"Upload failed"**
- ✅ Check internet connection
- ✅ Verify file is valid image format
- ✅ Ensure file size is under 5MB
- ✅ Check browser console for detailed errors

#### **"File too large"**
- ✅ Resize image before upload
- ✅ Use image compression tools
- ✅ Check file size in file explorer

### **Debug Steps:**

1. **Check Environment Variables:**
   ```javascript
   // Run in browser console
   console.log('Cloud Name:', process.env.NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME)
   console.log('Upload Preset:', process.env.NEXT_PUBLIC_CLOUDINARY_UPLOAD_PRESET)
   ```

2. **Test Cloudinary API:**
   ```bash
   # Test with curl
   curl -X POST \
     https://api.cloudinary.com/v1_1/YOUR_CLOUD_NAME/image/upload \
     -F "file=@test-image.jpg" \
     -F "upload_preset=anjali_cms_unsigned"
   ```

3. **Check Network Tab:**
   - Open browser DevTools → Network
   - Try upload and check for failed requests
   - Look at response details for error messages

## 📁 File Organization

Your uploads will be organized in Cloudinary as:

```
cloudinary.com/your-cloud/
├── anjali-cms/
│   ├── blogs/
│   ├── services/
│   ├── packages/
│   ├── gallery/
│   └── testimonials/
```

## 🔐 Security Notes

### **Unsigned Uploads**
- ✅ **Safe for public use**: No API secrets exposed
- ✅ **Folder restrictions**: Files go to specified folder only
- ✅ **Format restrictions**: Only image files allowed
- ✅ **Size limits**: 5MB maximum

### **For Production**
- Consider **signed uploads** for sensitive content
- Set up **moderation** for user-generated content
- Use **transformation URLs** for optimized delivery
- Enable **backup** for important images

## 🚀 Usage in Forms

The component works with any form library:

```typescript
// In your form component
import { ImageUpload } from '@/components/ui/image-upload'

function BlogForm() {
  const [imageUrl, setImageUrl] = useState('')

  return (
    <form>
      <ImageUpload
        value={imageUrl}
        onChange={setImageUrl}
        folder="blogs"
        label="Featured Image"
      />
    </form>
  )
}
```

---

**🎉 Your basic Cloudinary upload is now ready to use!**

No complex configurations or additional packages needed - just set your environment variables and start uploading!
