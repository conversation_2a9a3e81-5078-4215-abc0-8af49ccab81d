import Link from 'next/link'
import { ArrowLeft, Home, Search } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'

export default function NotFound() {
  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-cream via-white to-blush-pink-light px-4">
      <div className="text-center space-y-8 max-w-md mx-auto">
        {/* 404 Illustration */}
        <div className="relative">
          <div className="text-8xl md:text-9xl font-bold text-rose-gold/20 select-none">
            404
          </div>
          <div className="absolute inset-0 flex items-center justify-center">
            <div className="w-24 h-24 bg-gradient-to-br from-rose-gold to-blush-pink rounded-full flex items-center justify-center">
              <span className="text-white font-display font-bold text-3xl">A</span>
            </div>
          </div>
        </div>

        {/* Error Message */}
        <div className="space-y-4">
          <h1 className="font-display text-3xl md:text-4xl font-bold text-text-primary">
            Page Not Found
          </h1>
          <p className="text-text-secondary leading-relaxed">
            Oops! The page you're looking for doesn't exist. It might have been moved, 
            deleted, or you entered the wrong URL.
          </p>
        </div>

        {/* Action Buttons */}
        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <Button asChild variant="gradient" className="group">
            <Link href="/">
              <Home className="w-4 h-4 mr-2" />
              Go Home
            </Link>
          </Button>
          
          <Button asChild variant="outline">
            <Link href="/services">
              <Search className="w-4 h-4 mr-2" />
              Browse Services
            </Link>
          </Button>
        </div>

        {/* Quick Links */}
        <Card className="bg-white/80 backdrop-blur-sm border-0">
          <CardContent className="p-6">
            <h3 className="font-semibold text-text-primary mb-4">
              Popular Pages
            </h3>
            <div className="grid grid-cols-2 gap-3 text-sm">
              <Link 
                href="/about" 
                className="text-text-secondary hover:text-rose-gold-dark transition-colors"
              >
                About Us
              </Link>
              <Link 
                href="/services" 
                className="text-text-secondary hover:text-rose-gold-dark transition-colors"
              >
                Services
              </Link>
              <Link 
                href="/packages" 
                className="text-text-secondary hover:text-rose-gold-dark transition-colors"
              >
                Packages
              </Link>
              <Link 
                href="/portfolio" 
                className="text-text-secondary hover:text-rose-gold-dark transition-colors"
              >
                Portfolio
              </Link>
              <Link 
                href="/blog" 
                className="text-text-secondary hover:text-rose-gold-dark transition-colors"
              >
                Blog
              </Link>
              <Link 
                href="/contact" 
                className="text-text-secondary hover:text-rose-gold-dark transition-colors"
              >
                Contact
              </Link>
            </div>
          </CardContent>
        </Card>

        {/* Back Button */}
        <Button 
          variant="ghost" 
          onClick={() => window.history.back()}
          className="text-text-muted hover:text-text-secondary"
        >
          <ArrowLeft className="w-4 h-4 mr-2" />
          Go Back
        </Button>
      </div>
    </div>
  )
}
