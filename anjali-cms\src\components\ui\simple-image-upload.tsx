"use client"

import { useState, useRef } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Upload, X, Image as ImageIcon, Link } from 'lucide-react'
import Image from 'next/image'

interface SimpleImageUploadProps {
  value?: string
  onChange: (url: string) => void
  onRemove?: () => void
  disabled?: boolean
  label?: string
}

export function SimpleImageUpload({
  value,
  onChange,
  onRemove,
  disabled,
  label = "Image"
}: SimpleImageUploadProps) {
  const [imageUrl, setImageUrl] = useState(value || '')
  const [isValidUrl, setIsValidUrl] = useState(false)

  const handleUrlChange = (url: string) => {
    setImageUrl(url)
    
    // Basic URL validation
    const urlPattern = /^https?:\/\/.+\.(jpg|jpeg|png|gif|webp)(\?.*)?$/i
    const isValid = urlPattern.test(url)
    setIsValidUrl(isValid)
    
    if (isValid) {
      onChange(url)
    }
  }

  const handleRemove = () => {
    setImageUrl('')
    setIsValidUrl(false)
    if (onRemove) {
      onRemove()
    } else {
      onChange('')
    }
  }

  const suggestedImages = [
    'https://images.unsplash.com/photo-1596462502278-27bfdc403348?w=400&h=400&fit=crop&crop=face&q=80',
    'https://images.unsplash.com/photo-1487412947147-5cebf100ffc2?w=400&h=400&fit=crop&crop=face&q=80',
    'https://images.unsplash.com/photo-1616683693504-3ea7e9ad6fec?w=400&h=400&fit=crop&crop=face&q=80',
    'https://images.unsplash.com/photo-1522337360788-8b13dee7a37e?w=400&h=400&fit=crop&crop=face&q=80',
    'https://images.unsplash.com/photo-1594736797933-d0401ba2fe65?w=400&h=400&fit=crop&crop=face&q=80',
    'https://images.unsplash.com/photo-1515377905703-c4788e51af15?w=400&h=400&fit=crop&crop=face&q=80',
  ]

  return (
    <div className="space-y-4">
      <Label htmlFor="image-url">{label}</Label>
      
      {/* Current Image Display */}
      {value && (
        <div className="relative w-32 h-32 rounded-lg overflow-hidden border">
          <Image
            src={value}
            alt="Uploaded image"
            fill
            className="object-cover"
            onError={() => setIsValidUrl(false)}
          />
          <button
            type="button"
            onClick={handleRemove}
            className="absolute top-2 right-2 bg-red-500 text-white rounded-full p-1 hover:bg-red-600"
            disabled={disabled}
          >
            <X className="w-4 h-4" />
          </button>
        </div>
      )}

      {/* URL Input */}
      <div className="space-y-2">
        <div className="flex gap-2">
          <Input
            id="image-url"
            type="url"
            placeholder="https://example.com/image.jpg"
            value={imageUrl}
            onChange={(e) => handleUrlChange(e.target.value)}
            disabled={disabled}
            className={isValidUrl ? 'border-green-500' : imageUrl ? 'border-red-500' : ''}
          />
          <Button
            type="button"
            variant="outline"
            onClick={() => handleUrlChange(imageUrl)}
            disabled={disabled || !isValidUrl}
          >
            <Link className="w-4 h-4" />
          </Button>
        </div>
        
        {imageUrl && !isValidUrl && (
          <p className="text-sm text-red-600">
            Please enter a valid image URL (jpg, jpeg, png, gif, webp)
          </p>
        )}
      </div>

      {/* Suggested Images */}
      <div className="space-y-2">
        <Label className="text-sm text-gray-600">Quick Select (Sample Images):</Label>
        <div className="grid grid-cols-3 gap-2">
          {suggestedImages.map((url, index) => (
            <button
              key={index}
              type="button"
              onClick={() => handleUrlChange(url)}
              disabled={disabled}
              className="relative w-20 h-20 rounded border hover:border-blue-500 overflow-hidden"
            >
              <Image
                src={url}
                alt={`Sample ${index + 1}`}
                fill
                className="object-cover"
              />
            </button>
          ))}
        </div>
      </div>

      {/* Instructions */}
      <div className="text-sm text-gray-500 space-y-1">
        <p><strong>Options:</strong></p>
        <ul className="list-disc list-inside space-y-1">
          <li>Enter any image URL from the web</li>
          <li>Use suggested sample images above</li>
          <li>Upload to a service like Imgur, then paste the URL</li>
          <li>For production: Set up Cloudinary for proper image management</li>
        </ul>
      </div>
    </div>
  )
}
