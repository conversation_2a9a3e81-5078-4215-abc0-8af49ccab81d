"use client"

import { useState, useEffect } from 'react'
import { useParams } from 'next/navigation'
import { TestimonialForm } from '@/components/forms/testimonial-form'
import { toast } from 'sonner'

interface Testimonial {
  id: string
  name: string
  email?: string
  message: string
  rating: number
  image?: string
  service?: string
  status: 'PENDING' | 'APPROVED' | 'REJECTED'
}

export default function EditTestimonialPage() {
  const params = useParams()
  const [testimonial, setTestimonial] = useState<Testimonial | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const fetchTestimonial = async () => {
      try {
        const response = await fetch(`/api/testimonials/${params.id}`)
        if (response.ok) {
          const data = await response.json()
          setTestimonial(data)
        } else {
          toast.error('Testimonial not found')
        }
      } catch (error) {
        toast.error('Error fetching testimonial')
      } finally {
        setLoading(false)
      }
    }

    if (params.id) {
      fetchTestimonial()
    }
  }, [params.id])

  if (loading) {
    return <div>Loading...</div>
  }

  if (!testimonial) {
    return <div>Testimonial not found</div>
  }

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold">Edit Testimonial</h1>
        <p className="text-muted-foreground">
          Update the testimonial details and approval status
        </p>
      </div>

      <TestimonialForm initialData={testimonial} isEditing />
    </div>
  )
}
