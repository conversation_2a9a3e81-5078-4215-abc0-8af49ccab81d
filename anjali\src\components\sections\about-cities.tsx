'use client'

import { <PERSON><PERSON><PERSON>, <PERSON>, Car } from 'lucide-react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Section, SectionHeader } from '@/components/ui/section'
import { AnimatedElement, StaggeredContainer, StaggeredItem } from '@/components/ui/animated-element'
import { getServiceAreas } from '@/lib/data'
import { formatPrice } from '@/lib/utils'

export default function AboutCities() {
  const serviceAreas = getServiceAreas()

  return (
    <Section background="gradient">
      <AnimatedElement animation="fadeIn">
        <SectionHeader
          subtitle="Service Areas"
          title="Cities We Serve"
          description="Professional makeup services available across multiple cities in Nepal. Travel fees may apply for locations outside Biratnagar."
        />
      </AnimatedElement>

      <StaggeredContainer className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
        {serviceAreas.map((area, index) => (
          <StaggeredItem key={area.name}>
            <Card className={`h-full transition-all duration-300 hover:shadow-xl ${
              area.primary 
                ? 'bg-gradient-to-br from-rose-gold/20 to-blush-pink/20 border-rose-gold/30' 
                : 'bg-white/80 backdrop-blur-sm border-0'
            }`}>
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <CardTitle className="flex items-center gap-2 text-lg">
                    <MapPin className={`w-5 h-5 ${area.primary ? 'text-rose-gold-dark' : 'text-text-secondary'}`} />
                    {area.name}
                  </CardTitle>
                  {area.primary && (
                    <Badge variant="default" className="text-xs">
                      Home Base
                    </Badge>
                  )}
                </div>
              </CardHeader>
              
              <CardContent className="space-y-3">
                <div className="flex items-center gap-2 text-sm text-text-secondary">
                  <Car className="w-4 h-4" />
                  <span>
                    {area.travelFee === 0 
                      ? 'No travel fee' 
                      : `Travel fee: ${formatPrice(`NPR ${area.travelFee}`)}`
                    }
                  </span>
                </div>
                
                <div className="flex items-center gap-2 text-sm text-text-secondary">
                  <Clock className="w-4 h-4" />
                  <span>
                    {area.primary 
                      ? 'Same-day booking available' 
                      : 'Advance booking recommended'
                    }
                  </span>
                </div>

                {area.primary && (
                  <div className="pt-2 border-t border-rose-gold/20">
                    <p className="text-sm text-text-secondary">
                      Our main studio location with full equipment and product range available.
                    </p>
                  </div>
                )}
              </CardContent>
            </Card>
          </StaggeredItem>
        ))}
      </StaggeredContainer>

      {/* Additional Information */}
      <AnimatedElement animation="slideUp" delay={0.6} className="mt-12">
        <div className="grid md:grid-cols-3 gap-8 text-center">
          <div className="space-y-2">
            <div className="w-16 h-16 bg-gradient-to-br from-rose-gold to-blush-pink rounded-full flex items-center justify-center mx-auto">
              <MapPin className="w-8 h-8 text-white" />
            </div>
            <h3 className="font-display text-lg font-semibold text-text-primary">
              Wide Coverage
            </h3>
            <p className="text-text-secondary text-sm">
              Serving 7 major cities across Nepal with professional makeup services
            </p>
          </div>
          
          <div className="space-y-2">
            <div className="w-16 h-16 bg-gradient-to-br from-blush-pink to-lavender rounded-full flex items-center justify-center mx-auto">
              <Car className="w-8 h-8 text-white" />
            </div>
            <h3 className="font-display text-lg font-semibold text-text-primary">
              Travel Services
            </h3>
            <p className="text-text-secondary text-sm">
              We come to you! On-location services available with minimal travel fees
            </p>
          </div>
          
          <div className="space-y-2">
            <div className="w-16 h-16 bg-gradient-to-br from-lavender to-rose-gold rounded-full flex items-center justify-center mx-auto">
              <Clock className="w-8 h-8 text-white" />
            </div>
            <h3 className="font-display text-lg font-semibold text-text-primary">
              Flexible Scheduling
            </h3>
            <p className="text-text-secondary text-sm">
              Early morning and evening appointments available for your convenience
            </p>
          </div>
        </div>
      </AnimatedElement>

      {/* Service Notes */}
      <AnimatedElement animation="fadeIn" delay={0.8} className="mt-12">
        <Card className="bg-white/60 backdrop-blur-sm border-0">
          <CardContent className="p-6">
            <h3 className="font-display text-lg font-semibold text-text-primary mb-4 text-center">
              Service Information
            </h3>
            <div className="grid md:grid-cols-2 gap-6 text-sm text-text-secondary">
              <div>
                <h4 className="font-semibold text-text-primary mb-2">Booking Requirements:</h4>
                <ul className="space-y-1">
                  <li>• Advance booking recommended for all locations</li>
                  <li>• Minimum 24-hour notice for travel bookings</li>
                  <li>• Emergency bookings subject to availability</li>
                </ul>
              </div>
              <div>
                <h4 className="font-semibold text-text-primary mb-2">What's Included:</h4>
                <ul className="space-y-1">
                  <li>• Professional makeup application</li>
                  <li>• High-quality products and tools</li>
                  <li>• Touch-up kit for events</li>
                  <li>• Consultation and color matching</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>
      </AnimatedElement>
    </Section>
  )
}
