"use client"

import { useState, useEffect } from 'react'
import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation'
import Link from 'next/link'
import Image from 'next/image'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Separator } from '@/components/ui/separator'
import { Edit, ArrowLeft, Calendar, User, Clock, Tag } from 'lucide-react'
import { formatDate } from '@/lib/utils'
import { toast } from 'sonner'

interface Blog {
  id: string
  title: string
  slug: string
  excerpt?: string
  content: string
  author: string
  featured: boolean
  image?: string
  readTime?: string
  status: 'DRAFT' | 'PUBLISHED' | 'ARCHIVED'
  metaTitle?: string
  metaDescription?: string
  keywords: string[]
  createdAt: string
  updatedAt: string
  publishedAt?: string
  category?: {
    id: string
    name: string
  }
  tags: Array<{
    tag: {
      id: string
      name: string
    }
  }>
}

export default function BlogViewPage() {
  const params = useParams()
  const router = useRouter()
  const [blog, setBlog] = useState<Blog | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const fetchBlog = async () => {
      try {
        const response = await fetch(`/api/blogs/${params.id}`)
        if (response.ok) {
          const data = await response.json()
          setBlog(data)
        } else {
          toast.error('Blog not found')
          router.push('/dashboard/blogs')
        }
      } catch (error) {
        toast.error('Error fetching blog')
        router.push('/dashboard/blogs')
      } finally {
        setLoading(false)
      }
    }

    if (params.id) {
      fetchBlog()
    }
  }, [params.id, router])

  if (loading) {
    return <div>Loading...</div>
  }

  if (!blog) {
    return <div>Blog not found</div>
  }

  const getStatusBadge = (status: string) => {
    const variants = {
      DRAFT: 'secondary',
      PUBLISHED: 'default',
      ARCHIVED: 'outline',
    } as const

    return (
      <Badge variant={variants[status as keyof typeof variants]}>
        {status}
      </Badge>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="ghost" size="sm" onClick={() => router.back()}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back
          </Button>
          <div>
            <h1 className="text-3xl font-bold">{blog.title}</h1>
            <div className="flex items-center gap-4 text-sm text-muted-foreground mt-2">
              <div className="flex items-center gap-1">
                <User className="h-4 w-4" />
                {blog.author}
              </div>
              <div className="flex items-center gap-1">
                <Calendar className="h-4 w-4" />
                {formatDate(blog.createdAt)}
              </div>
              {blog.readTime && (
                <div className="flex items-center gap-1">
                  <Clock className="h-4 w-4" />
                  {blog.readTime}
                </div>
              )}
            </div>
          </div>
        </div>
        <div className="flex items-center gap-2">
          {getStatusBadge(blog.status)}
          {blog.featured && (
            <Badge variant="outline">Featured</Badge>
          )}
          <Button asChild>
            <Link href={`/dashboard/blogs/${blog.id}/edit`}>
              <Edit className="h-4 w-4 mr-2" />
              Edit
            </Link>
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div className="lg:col-span-2 space-y-6">
          {blog.image && (
            <Card>
              <CardContent className="p-0">
                <div className="relative aspect-video w-full overflow-hidden rounded-lg">
                  <Image
                    src={blog.image}
                    alt={blog.title}
                    fill
                    className="object-cover"
                  />
                </div>
              </CardContent>
            </Card>
          )}

          {blog.excerpt && (
            <Card>
              <CardHeader>
                <CardTitle>Excerpt</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground">{blog.excerpt}</p>
              </CardContent>
            </Card>
          )}

          <Card>
            <CardHeader>
              <CardTitle>Content</CardTitle>
            </CardHeader>
            <CardContent>
              <div 
                className="prose prose-sm sm:prose lg:prose-lg xl:prose-2xl max-w-none"
                dangerouslySetInnerHTML={{ __html: blog.content }}
              />
            </CardContent>
          </Card>
        </div>

        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Details</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label className="text-sm font-medium">Status</Label>
                <div className="mt-1">
                  {getStatusBadge(blog.status)}
                </div>
              </div>

              {blog.category && (
                <div>
                  <Label className="text-sm font-medium">Category</Label>
                  <div className="mt-1">
                    <Badge variant="outline">{blog.category.name}</Badge>
                  </div>
                </div>
              )}

              {blog.tags.length > 0 && (
                <div>
                  <Label className="text-sm font-medium">Tags</Label>
                  <div className="flex flex-wrap gap-2 mt-1">
                    {blog.tags.map((tagRelation) => (
                      <Badge key={tagRelation.tag.id} variant="outline">
                        <Tag className="h-3 w-3 mr-1" />
                        {tagRelation.tag.name}
                      </Badge>
                    ))}
                  </div>
                </div>
              )}

              <Separator />

              <div className="space-y-2 text-sm">
                <div>
                  <span className="font-medium">Created:</span>{' '}
                  {formatDate(blog.createdAt)}
                </div>
                <div>
                  <span className="font-medium">Updated:</span>{' '}
                  {formatDate(blog.updatedAt)}
                </div>
                {blog.publishedAt && (
                  <div>
                    <span className="font-medium">Published:</span>{' '}
                    {formatDate(blog.publishedAt)}
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          {(blog.metaTitle || blog.metaDescription || blog.keywords.length > 0) && (
            <Card>
              <CardHeader>
                <CardTitle>SEO Settings</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {blog.metaTitle && (
                  <div>
                    <Label className="text-sm font-medium">Meta Title</Label>
                    <p className="text-sm text-muted-foreground mt-1">
                      {blog.metaTitle}
                    </p>
                  </div>
                )}

                {blog.metaDescription && (
                  <div>
                    <Label className="text-sm font-medium">Meta Description</Label>
                    <p className="text-sm text-muted-foreground mt-1">
                      {blog.metaDescription}
                    </p>
                  </div>
                )}

                {blog.keywords.length > 0 && (
                  <div>
                    <Label className="text-sm font-medium">Keywords</Label>
                    <p className="text-sm text-muted-foreground mt-1">
                      {blog.keywords.join(', ')}
                    </p>
                  </div>
                )}
              </CardContent>
            </Card>
          )}
        </div>
      </div>
    </div>
  )
}

function Label({ children, className }: { children: React.ReactNode; className?: string }) {
  return <div className={className}>{children}</div>
}
