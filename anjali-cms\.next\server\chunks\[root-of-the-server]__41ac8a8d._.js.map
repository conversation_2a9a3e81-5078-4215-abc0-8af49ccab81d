{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 154, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/anjali-portfolio/anjali-cms/src/lib/prisma.ts"], "sourcesContent": ["import { PrismaClient } from '@prisma/client'\n\nconst globalForPrisma = globalThis as unknown as {\n  prisma: PrismaClient | undefined\n}\n\nexport const prisma = globalForPrisma.prisma ?? new PrismaClient()\n\nif (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,kBAAkB;AAIjB,MAAM,SAAS,gBAAgB,MAAM,IAAI,IAAI,6HAAA,CAAA,eAAY;AAEhE,wCAA2C,gBAAgB,MAAM,GAAG", "debugId": null}}, {"offset": {"line": 166, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/anjali-portfolio/anjali-cms/src/lib/auth.ts"], "sourcesContent": ["import { NextAuthOptions } from \"next-auth\"\nimport Cred<PERSON><PERSON><PERSON>rovider from \"next-auth/providers/credentials\"\nimport { PrismaAdapter } from \"@auth/prisma-adapter\"\nimport { prisma } from \"./prisma\"\nimport bcrypt from \"bcryptjs\"\n\nexport const authOptions: NextAuthOptions = {\n  adapter: PrismaAdapter(prisma) as any,\n  providers: [\n    CredentialsProvider({\n      name: \"credentials\",\n      credentials: {\n        email: { label: \"Email\", type: \"email\" },\n        password: { label: \"Password\", type: \"password\" }\n      },\n      async authorize(credentials) {\n        if (!credentials?.email || !credentials?.password) {\n          return null\n        }\n\n        const user = await prisma.user.findUnique({\n          where: {\n            email: credentials.email\n          }\n        })\n\n        if (!user) {\n          return null\n        }\n\n        const isPasswordValid = await bcrypt.compare(\n          credentials.password,\n          user.password\n        )\n\n        if (!isPasswordValid) {\n          return null\n        }\n\n        return {\n          id: user.id,\n          email: user.email,\n          name: user.name,\n          role: user.role,\n        }\n      }\n    })\n  ],\n  session: {\n    strategy: \"jwt\"\n  },\n  callbacks: {\n    async jwt({ token, user }) {\n      if (user) {\n        token.role = user.role\n      }\n      return token\n    },\n    async session({ session, token }) {\n      if (token) {\n        session.user.id = token.sub!\n        session.user.role = token.role as string\n      }\n      return session\n    }\n  },\n  pages: {\n    signIn: \"/auth/signin\",\n  }\n}\n"], "names": [], "mappings": ";;;AACA;AACA;AACA;AACA;;;;;AAEO,MAAM,cAA+B;IAC1C,SAAS,CAAA,GAAA,sJAAA,CAAA,gBAAa,AAAD,EAAE,sHAAA,CAAA,SAAM;IAC7B,WAAW;QACT,CAAA,GAAA,0JAAA,CAAA,UAAmB,AAAD,EAAE;YAClB,MAAM;YACN,aAAa;gBACX,OAAO;oBAAE,OAAO;oBAAS,MAAM;gBAAQ;gBACvC,UAAU;oBAAE,OAAO;oBAAY,MAAM;gBAAW;YAClD;YACA,MAAM,WAAU,WAAW;gBACzB,IAAI,CAAC,aAAa,SAAS,CAAC,aAAa,UAAU;oBACjD,OAAO;gBACT;gBAEA,MAAM,OAAO,MAAM,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,UAAU,CAAC;oBACxC,OAAO;wBACL,OAAO,YAAY,KAAK;oBAC1B;gBACF;gBAEA,IAAI,CAAC,MAAM;oBACT,OAAO;gBACT;gBAEA,MAAM,kBAAkB,MAAM,mIAAA,CAAA,UAAM,CAAC,OAAO,CAC1C,YAAY,QAAQ,EACpB,KAAK,QAAQ;gBAGf,IAAI,CAAC,iBAAiB;oBACpB,OAAO;gBACT;gBAEA,OAAO;oBACL,IAAI,KAAK,EAAE;oBACX,OAAO,KAAK,KAAK;oBACjB,MAAM,KAAK,IAAI;oBACf,MAAM,KAAK,IAAI;gBACjB;YACF;QACF;KACD;IACD,SAAS;QACP,UAAU;IACZ;IACA,WAAW;QACT,MAAM,KAAI,EAAE,KAAK,EAAE,IAAI,EAAE;YACvB,IAAI,MAAM;gBACR,MAAM,IAAI,GAAG,KAAK,IAAI;YACxB;YACA,OAAO;QACT;QACA,MAAM,SAAQ,EAAE,OAAO,EAAE,KAAK,EAAE;YAC9B,IAAI,OAAO;gBACT,QAAQ,IAAI,CAAC,EAAE,GAAG,MAAM,GAAG;gBAC3B,QAAQ,IAAI,CAAC,IAAI,GAAG,MAAM,IAAI;YAChC;YACA,OAAO;QACT;IACF;IACA,OAAO;QACL,QAAQ;IACV;AACF", "debugId": null}}, {"offset": {"line": 243, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/anjali-portfolio/anjali-cms/src/lib/cors.ts"], "sourcesContent": ["import { NextResponse } from 'next/server'\n\nexport const corsHeaders = {\n  'Access-Control-Allow-Origin': '*',\n  'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',\n  'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-Requested-With',\n  'Access-Control-Max-Age': '86400',\n}\n\nexport function withCors(response: NextResponse) {\n  Object.entries(corsHeaders).forEach(([key, value]) => {\n    response.headers.set(key, value)\n  })\n  return response\n}\n\nexport function corsResponse(data: any, init?: ResponseInit) {\n  return NextResponse.json(data, {\n    ...init,\n    headers: {\n      ...corsHeaders,\n      ...init?.headers,\n    },\n  })\n}\n\nexport function corsOptionsResponse() {\n  return new NextResponse(null, {\n    status: 200,\n    headers: corsHeaders,\n  })\n}\n"], "names": [], "mappings": ";;;;;;AAAA;;AAEO,MAAM,cAAc;IACzB,+BAA+B;IAC/B,gCAAgC;IAChC,gCAAgC;IAChC,0BAA0B;AAC5B;AAEO,SAAS,SAAS,QAAsB;IAC7C,OAAO,OAAO,CAAC,aAAa,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;QAC/C,SAAS,OAAO,CAAC,GAAG,CAAC,KAAK;IAC5B;IACA,OAAO;AACT;AAEO,SAAS,aAAa,IAAS,EAAE,IAAmB;IACzD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC,MAAM;QAC7B,GAAG,IAAI;QACP,SAAS;YACP,GAAG,WAAW;YACd,GAAG,MAAM,OAAO;QAClB;IACF;AACF;AAEO,SAAS;IACd,OAAO,IAAI,gIAAA,CAAA,eAAY,CAAC,MAAM;QAC5B,QAAQ;QACR,SAAS;IACX;AACF", "debugId": null}}, {"offset": {"line": 282, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/anjali-portfolio/anjali-cms/src/app/api/testimonials/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\nimport { getServerSession } from 'next-auth'\nimport { authOptions } from '@/lib/auth'\nimport { prisma } from '@/lib/prisma'\nimport { z } from 'zod'\nimport { corsResponse, corsOptionsResponse } from '@/lib/cors'\n\nconst testimonialSchema = z.object({\n  name: z.string().min(1, 'Name is required'),\n  email: z.string().email('Valid email is required').optional(),\n  message: z.string().min(1, 'Message is required'),\n  rating: z.number().min(1).max(5).default(5),\n  image: z.string().optional(),\n  service: z.string().optional(),\n  status: z.enum(['PENDING', 'APPROVED', 'REJECTED']).default('PENDING'),\n})\n\nexport async function OPTIONS() {\n  return corsOptionsResponse()\n}\n\nexport async function GET(request: NextRequest) {\n  try {\n    // Public endpoint - no authentication required\n\n    const { searchParams } = new URL(request.url)\n    const page = parseInt(searchParams.get('page') || '1')\n    const limit = parseInt(searchParams.get('limit') || '10')\n    const search = searchParams.get('search') || ''\n    const status = searchParams.get('status') || ''\n    const service = searchParams.get('service') || ''\n\n    const skip = (page - 1) * limit\n\n    const where: any = {}\n    \n    if (search) {\n      where.OR = [\n        { name: { contains: search, mode: 'insensitive' } },\n        { message: { contains: search, mode: 'insensitive' } },\n        { service: { contains: search, mode: 'insensitive' } },\n      ]\n    }\n    \n    if (status) {\n      where.status = status\n    }\n    \n    if (service) {\n      where.service = { contains: service, mode: 'insensitive' }\n    }\n\n    const [testimonials, total] = await Promise.all([\n      prisma.testimonial.findMany({\n        where,\n        orderBy: [\n          { status: 'asc' }, // Pending first\n          { createdAt: 'desc' }\n        ],\n        skip,\n        take: limit,\n      }),\n      prisma.testimonial.count({ where }),\n    ])\n\n    return corsResponse({\n      testimonials,\n      pagination: {\n        page,\n        limit,\n        total,\n        pages: Math.ceil(total / limit),\n      },\n    })\n  } catch (error) {\n    console.error('Error fetching testimonials:', error)\n    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })\n  }\n}\n\nexport async function POST(request: NextRequest) {\n  try {\n    const session = await getServerSession(authOptions)\n    \n    if (!session) {\n      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })\n    }\n\n    const body = await request.json()\n    const validatedData = testimonialSchema.parse(body)\n\n    const testimonial = await prisma.testimonial.create({\n      data: validatedData,\n    })\n\n    return NextResponse.json(testimonial, { status: 201 })\n  } catch (error) {\n    if (error instanceof z.ZodError) {\n      return NextResponse.json({ error: error.errors }, { status: 400 })\n    }\n    \n    console.error('Error creating testimonial:', error)\n    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })\n  }\n}\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AACA;AACA;AACA;;;;;;;AAEA,MAAM,oBAAoB,+KAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACjC,MAAM,+KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IACxB,OAAO,+KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,KAAK,CAAC,2BAA2B,QAAQ;IAC3D,SAAS,+KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC3B,QAAQ,+KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,GAAG,OAAO,CAAC;IACzC,OAAO,+KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC1B,SAAS,+KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC5B,QAAQ,+KAAA,CAAA,IAAC,CAAC,IAAI,CAAC;QAAC;QAAW;QAAY;KAAW,EAAE,OAAO,CAAC;AAC9D;AAEO,eAAe;IACpB,OAAO,CAAA,GAAA,oHAAA,CAAA,sBAAmB,AAAD;AAC3B;AAEO,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,+CAA+C;QAE/C,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;QAC5C,MAAM,OAAO,SAAS,aAAa,GAAG,CAAC,WAAW;QAClD,MAAM,QAAQ,SAAS,aAAa,GAAG,CAAC,YAAY;QACpD,MAAM,SAAS,aAAa,GAAG,CAAC,aAAa;QAC7C,MAAM,SAAS,aAAa,GAAG,CAAC,aAAa;QAC7C,MAAM,UAAU,aAAa,GAAG,CAAC,cAAc;QAE/C,MAAM,OAAO,CAAC,OAAO,CAAC,IAAI;QAE1B,MAAM,QAAa,CAAC;QAEpB,IAAI,QAAQ;YACV,MAAM,EAAE,GAAG;gBACT;oBAAE,MAAM;wBAAE,UAAU;wBAAQ,MAAM;oBAAc;gBAAE;gBAClD;oBAAE,SAAS;wBAAE,UAAU;wBAAQ,MAAM;oBAAc;gBAAE;gBACrD;oBAAE,SAAS;wBAAE,UAAU;wBAAQ,MAAM;oBAAc;gBAAE;aACtD;QACH;QAEA,IAAI,QAAQ;YACV,MAAM,MAAM,GAAG;QACjB;QAEA,IAAI,SAAS;YACX,MAAM,OAAO,GAAG;gBAAE,UAAU;gBAAS,MAAM;YAAc;QAC3D;QAEA,MAAM,CAAC,cAAc,MAAM,GAAG,MAAM,QAAQ,GAAG,CAAC;YAC9C,sHAAA,CAAA,SAAM,CAAC,WAAW,CAAC,QAAQ,CAAC;gBAC1B;gBACA,SAAS;oBACP;wBAAE,QAAQ;oBAAM;oBAChB;wBAAE,WAAW;oBAAO;iBACrB;gBACD;gBACA,MAAM;YACR;YACA,sHAAA,CAAA,SAAM,CAAC,WAAW,CAAC,KAAK,CAAC;gBAAE;YAAM;SAClC;QAED,OAAO,CAAA,GAAA,oHAAA,CAAA,eAAY,AAAD,EAAE;YAClB;YACA,YAAY;gBACV;gBACA;gBACA;gBACA,OAAO,KAAK,IAAI,CAAC,QAAQ;YAC3B;QACF;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,gCAAgC;QAC9C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YAAE,OAAO;QAAwB,GAAG;YAAE,QAAQ;QAAI;IAC7E;AACF;AAEO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,UAAU,MAAM,CAAA,GAAA,uIAAA,CAAA,mBAAgB,AAAD,EAAE,oHAAA,CAAA,cAAW;QAElD,IAAI,CAAC,SAAS;YACZ,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAe,GAAG;gBAAE,QAAQ;YAAI;QACpE;QAEA,MAAM,OAAO,MAAM,QAAQ,IAAI;QAC/B,MAAM,gBAAgB,kBAAkB,KAAK,CAAC;QAE9C,MAAM,cAAc,MAAM,sHAAA,CAAA,SAAM,CAAC,WAAW,CAAC,MAAM,CAAC;YAClD,MAAM;QACR;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC,aAAa;YAAE,QAAQ;QAAI;IACtD,EAAE,OAAO,OAAO;QACd,IAAI,iBAAiB,+KAAA,CAAA,IAAC,CAAC,QAAQ,EAAE;YAC/B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO,MAAM,MAAM;YAAC,GAAG;gBAAE,QAAQ;YAAI;QAClE;QAEA,QAAQ,KAAK,CAAC,+BAA+B;QAC7C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YAAE,OAAO;QAAwB,GAAG;YAAE,QAAQ;QAAI;IAC7E;AACF", "debugId": null}}]}