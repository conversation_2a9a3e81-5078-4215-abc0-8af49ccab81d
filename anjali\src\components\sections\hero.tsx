'use client'

import Link from 'next/link'
import Image from 'next/image'
import { motion } from 'framer-motion'
import { ArrowRight, Star, MapPin, Phone } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { getSiteConfig } from '@/lib/data'
import { generateWhatsAppLink } from '@/lib/utils'

export default function Hero() {
  const siteConfig = getSiteConfig()
  const whatsappLink = generateWhatsAppLink(
    siteConfig.contact.whatsapp,
    siteConfig.whatsappMessage
  )

  return (
    <section className="relative min-h-screen flex items-center bg-gradient-to-br from-cream via-white to-blush-pink-light overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute top-20 left-10 w-32 h-32 bg-rose-gold rounded-full blur-3xl"></div>
        <div className="absolute bottom-20 right-10 w-40 h-40 bg-lavender rounded-full blur-3xl"></div>
        <div className="absolute top-1/2 left-1/3 w-24 h-24 bg-blush-pink rounded-full blur-2xl"></div>
      </div>

      <div className="relative mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 py-20">
        <div className="grid lg:grid-cols-2 gap-12 items-center">
          {/* Content */}
          <motion.div
            initial={{ opacity: 0, x: -50 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8, ease: 'easeOut' }}
            className="space-y-8"
          >
            {/* Badge */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
            >
              <Badge variant="secondary" className="text-sm px-4 py-2">
                <Star className="w-4 h-4 mr-2 fill-current" />
                Professional Makeup Artist in Nepal
              </Badge>
            </motion.div>

            {/* Main Heading */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.3 }}
              className="space-y-4"
            >
              <h1 className="font-display text-4xl md:text-5xl lg:text-6xl font-bold text-text-primary leading-tight">
                Transform Your
                <span className="block text-transparent bg-gradient-to-r from-rose-gold to-blush-pink bg-clip-text">
                  Natural Beauty
                </span>
              </h1>
              <p className="text-lg md:text-xl text-text-secondary leading-relaxed max-w-lg">
                Expert makeup artistry for your most special moments. Serving Biratnagar, 
                Itahari, Dharan, and surrounding areas with professional beauty services.
              </p>
            </motion.div>

            {/* Location & Rating */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.5 }}
              className="flex flex-wrap items-center gap-6 text-sm text-text-secondary"
            >
              <div className="flex items-center gap-2">
                <MapPin className="w-4 h-4 text-rose-gold-dark" />
                <span>Based in Biratnagar, Nepal</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="flex text-yellow-400">
                  {[...Array(5)].map((_, i) => (
                    <Star key={i} className="w-4 h-4 fill-current" />
                  ))}
                </div>
                <span>5.0 (50+ Reviews)</span>
              </div>
            </motion.div>

            {/* CTA Buttons */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.6 }}
              className="flex flex-col sm:flex-row gap-4"
            >
              <Button asChild variant="gradient" size="xl" className="group">
                <Link href={whatsappLink} target="_blank" rel="noopener noreferrer">
                  <Phone className="w-5 h-5 mr-2" />
                  Book Consultation
                  <ArrowRight className="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform" />
                </Link>
              </Button>
              <Button asChild variant="outline" size="xl">
                <Link href="/portfolio">
                  View Portfolio
                </Link>
              </Button>
            </motion.div>

            {/* Service Areas */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.8 }}
              className="pt-4"
            >
              <p className="text-sm text-text-muted mb-2">Service Areas:</p>
              <div className="flex flex-wrap gap-2">
                {siteConfig.serviceAreas.slice(0, 4).map((area) => (
                  <Badge key={area.name} variant="outline" className="text-xs">
                    {area.name}
                  </Badge>
                ))}
                <Badge variant="outline" className="text-xs">
                  +3 more
                </Badge>
              </div>
            </motion.div>
          </motion.div>

          {/* Hero Image */}
          <motion.div
            initial={{ opacity: 0, x: 50 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8, delay: 0.4 }}
            className="relative"
          >
            <div className="relative aspect-[4/5] rounded-2xl overflow-hidden shadow-2xl">
              <Image
                src="https://images.unsplash.com/photo-1596462502278-27bfdc403348?w=600&h=750&fit=crop&crop=face"
                alt="Professional makeup artist at work"
                fill
                className="object-cover"
                priority
              />
              
              {/* Floating Elements */}
              <motion.div
                initial={{ opacity: 0, scale: 0 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.6, delay: 1.2 }}
                className="absolute top-6 right-6 bg-white/90 backdrop-blur-sm rounded-lg p-3 shadow-lg"
              >
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                  <span className="text-xs font-medium text-text-primary">Available Today</span>
                </div>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, scale: 0 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.6, delay: 1.4 }}
                className="absolute bottom-6 left-6 bg-white/90 backdrop-blur-sm rounded-lg p-3 shadow-lg"
              >
                <div className="text-center">
                  <div className="text-lg font-bold text-text-primary">5+</div>
                  <div className="text-xs text-text-secondary">Years Experience</div>
                </div>
              </motion.div>
            </div>

            {/* Decorative Elements */}
            <motion.div
              initial={{ opacity: 0, rotate: -180 }}
              animate={{ opacity: 1, rotate: 0 }}
              transition={{ duration: 1, delay: 1 }}
              className="absolute -top-4 -left-4 w-24 h-24 bg-gradient-to-br from-rose-gold to-blush-pink rounded-full opacity-20 blur-xl"
            ></motion.div>
            <motion.div
              initial={{ opacity: 0, rotate: 180 }}
              animate={{ opacity: 1, rotate: 0 }}
              transition={{ duration: 1, delay: 1.2 }}
              className="absolute -bottom-4 -right-4 w-32 h-32 bg-gradient-to-br from-lavender to-blush-pink rounded-full opacity-20 blur-xl"
            ></motion.div>
          </motion.div>
        </div>
      </div>
    </section>
  )
}
