"use client"

import { useState, useEffect } from 'react'
import { useParams } from 'next/navigation'
import { GalleryForm } from '@/components/forms/gallery-form'
import { toast } from 'sonner'

interface GalleryItem {
  id: string
  title: string
  description?: string
  image: string
  category?: string
  tags: string[]
  featured: boolean
  status: 'ACTIVE' | 'INACTIVE' | 'ARCHIVED'
}

export default function EditGalleryPage() {
  const params = useParams()
  const [galleryItem, setGalleryItem] = useState<GalleryItem | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const fetchGalleryItem = async () => {
      try {
        const response = await fetch(`/api/gallery/${params.id}`)
        if (response.ok) {
          const data = await response.json()
          setGalleryItem(data)
        } else {
          toast.error('Gallery item not found')
        }
      } catch (error) {
        toast.error('Error fetching gallery item')
      } finally {
        setLoading(false)
      }
    }

    if (params.id) {
      fetchGalleryItem()
    }
  }, [params.id])

  if (loading) {
    return <div>Loading...</div>
  }

  if (!galleryItem) {
    return <div>Gallery item not found</div>
  }

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold">Edit Gallery Item</h1>
        <p className="text-muted-foreground">
          Update the details and settings for this gallery image
        </p>
      </div>

      <GalleryForm initialData={galleryItem} isEditing />
    </div>
  )
}
