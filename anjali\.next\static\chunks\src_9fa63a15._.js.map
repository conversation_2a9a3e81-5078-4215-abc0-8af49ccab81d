{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/anjali-portfolio/anjali/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center rounded-md border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2\",\n  {\n    variants: {\n      variant: {\n        default: \"border-transparent bg-rose-gold text-white shadow hover:bg-rose-gold-dark\",\n        secondary: \"border-transparent bg-blush-pink text-text-primary hover:bg-blush-pink-dark\",\n        destructive: \"border-transparent bg-red-500 text-white shadow hover:bg-red-600\",\n        outline: \"text-text-primary border-gray-300\",\n        success: \"border-transparent bg-green-500 text-white shadow hover:bg-green-600\",\n        warning: \"border-transparent bg-yellow-500 text-white shadow hover:bg-yellow-600\",\n        lavender: \"border-transparent bg-lavender text-text-primary hover:bg-lavender-dark\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nexport interface BadgeProps\n  extends React.HTMLAttributes<HTMLDivElement>,\n    VariantProps<typeof badgeVariants> {}\n\nfunction Badge({ className, variant, ...props }: BadgeProps) {\n  return (\n    <div className={cn(badgeVariants({ variant }), className)} {...props} />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACtB,wKACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,WAAW;YACX,aAAa;YACb,SAAS;YACT,SAAS;YACT,SAAS;YACT,UAAU;QACZ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAOF,SAAS,MAAM,KAA4C;QAA5C,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,OAAmB,GAA5C;IACb,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QAAa,GAAG,KAAK;;;;;;AAExE;KAJS", "debugId": null}}, {"offset": {"line": 59, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/anjali-portfolio/anjali/src/components/sections/hero.tsx"], "sourcesContent": ["'use client'\n\nimport Link from 'next/link'\nimport Image from 'next/image'\nimport { motion } from 'framer-motion'\nimport { ArrowRight, Star, MapPin, Phone } from 'lucide-react'\nimport { Button } from '@/components/ui/button'\nimport { Badge } from '@/components/ui/badge'\nimport { getSiteConfig } from '@/lib/data'\nimport { generateWhatsAppLink } from '@/lib/utils'\n\nexport default function Hero() {\n  const siteConfig = getSiteConfig()\n  const whatsappLink = generateWhatsAppLink(\n    siteConfig.contact.whatsapp,\n    siteConfig.whatsappMessage\n  )\n\n  return (\n    <section className=\"relative min-h-screen flex items-center bg-gradient-to-br from-cream via-white to-blush-pink-light overflow-hidden\">\n      {/* Background Pattern */}\n      <div className=\"absolute inset-0 opacity-5\">\n        <div className=\"absolute top-20 left-10 w-32 h-32 bg-rose-gold rounded-full blur-3xl\"></div>\n        <div className=\"absolute bottom-20 right-10 w-40 h-40 bg-lavender rounded-full blur-3xl\"></div>\n        <div className=\"absolute top-1/2 left-1/3 w-24 h-24 bg-blush-pink rounded-full blur-2xl\"></div>\n      </div>\n\n      <div className=\"relative mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 py-20\">\n        <div className=\"grid lg:grid-cols-2 gap-12 items-center\">\n          {/* Content */}\n          <motion.div\n            initial={{ opacity: 0, x: -50 }}\n            animate={{ opacity: 1, x: 0 }}\n            transition={{ duration: 0.8, ease: 'easeOut' }}\n            className=\"space-y-8\"\n          >\n            {/* Badge */}\n            <motion.div\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.6, delay: 0.2 }}\n            >\n              <Badge variant=\"secondary\" className=\"text-sm px-4 py-2\">\n                <Star className=\"w-4 h-4 mr-2 fill-current\" />\n                Professional Makeup Artist in Nepal\n              </Badge>\n            </motion.div>\n\n            {/* Main Heading */}\n            <motion.div\n              initial={{ opacity: 0, y: 30 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.8, delay: 0.3 }}\n              className=\"space-y-4\"\n            >\n              <h1 className=\"font-display text-4xl md:text-5xl lg:text-6xl font-bold text-text-primary leading-tight\">\n                Transform Your\n                <span className=\"block text-transparent bg-gradient-to-r from-rose-gold to-blush-pink bg-clip-text\">\n                  Natural Beauty\n                </span>\n              </h1>\n              <p className=\"text-lg md:text-xl text-text-secondary leading-relaxed max-w-lg\">\n                Expert makeup artistry for your most special moments. Serving Biratnagar, \n                Itahari, Dharan, and surrounding areas with professional beauty services.\n              </p>\n            </motion.div>\n\n            {/* Location & Rating */}\n            <motion.div\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.6, delay: 0.5 }}\n              className=\"flex flex-wrap items-center gap-6 text-sm text-text-secondary\"\n            >\n              <div className=\"flex items-center gap-2\">\n                <MapPin className=\"w-4 h-4 text-rose-gold-dark\" />\n                <span>Based in Biratnagar, Nepal</span>\n              </div>\n              <div className=\"flex items-center gap-2\">\n                <div className=\"flex text-yellow-400\">\n                  {[...Array(5)].map((_, i) => (\n                    <Star key={i} className=\"w-4 h-4 fill-current\" />\n                  ))}\n                </div>\n                <span>5.0 (50+ Reviews)</span>\n              </div>\n            </motion.div>\n\n            {/* CTA Buttons */}\n            <motion.div\n              initial={{ opacity: 0, y: 30 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.8, delay: 0.6 }}\n              className=\"flex flex-col sm:flex-row gap-4\"\n            >\n              <Button asChild variant=\"gradient\" size=\"xl\" className=\"group\">\n                <Link href={whatsappLink} target=\"_blank\" rel=\"noopener noreferrer\">\n                  <Phone className=\"w-5 h-5 mr-2\" />\n                  Book Consultation\n                  <ArrowRight className=\"w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform\" />\n                </Link>\n              </Button>\n              <Button asChild variant=\"outline\" size=\"xl\">\n                <Link href=\"/portfolio\">\n                  View Portfolio\n                </Link>\n              </Button>\n            </motion.div>\n\n            {/* Service Areas */}\n            <motion.div\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.6, delay: 0.8 }}\n              className=\"pt-4\"\n            >\n              <p className=\"text-sm text-text-muted mb-2\">Service Areas:</p>\n              <div className=\"flex flex-wrap gap-2\">\n                {siteConfig.serviceAreas.slice(0, 4).map((area) => (\n                  <Badge key={area.name} variant=\"outline\" className=\"text-xs\">\n                    {area.name}\n                  </Badge>\n                ))}\n                <Badge variant=\"outline\" className=\"text-xs\">\n                  +3 more\n                </Badge>\n              </div>\n            </motion.div>\n          </motion.div>\n\n          {/* Hero Image */}\n          <motion.div\n            initial={{ opacity: 0, x: 50 }}\n            animate={{ opacity: 1, x: 0 }}\n            transition={{ duration: 0.8, delay: 0.4 }}\n            className=\"relative\"\n          >\n            <div className=\"relative aspect-[4/5] rounded-2xl overflow-hidden shadow-2xl\">\n              <Image\n                src=\"https://images.unsplash.com/photo-1596462502278-27bfdc403348?w=600&h=750&fit=crop&crop=face\"\n                alt=\"Professional makeup artist at work\"\n                fill\n                className=\"object-cover\"\n                priority\n              />\n              \n              {/* Floating Elements */}\n              <motion.div\n                initial={{ opacity: 0, scale: 0 }}\n                animate={{ opacity: 1, scale: 1 }}\n                transition={{ duration: 0.6, delay: 1.2 }}\n                className=\"absolute top-6 right-6 bg-white/90 backdrop-blur-sm rounded-lg p-3 shadow-lg\"\n              >\n                <div className=\"flex items-center gap-2\">\n                  <div className=\"w-2 h-2 bg-green-500 rounded-full animate-pulse\"></div>\n                  <span className=\"text-xs font-medium text-text-primary\">Available Today</span>\n                </div>\n              </motion.div>\n\n              <motion.div\n                initial={{ opacity: 0, scale: 0 }}\n                animate={{ opacity: 1, scale: 1 }}\n                transition={{ duration: 0.6, delay: 1.4 }}\n                className=\"absolute bottom-6 left-6 bg-white/90 backdrop-blur-sm rounded-lg p-3 shadow-lg\"\n              >\n                <div className=\"text-center\">\n                  <div className=\"text-lg font-bold text-text-primary\">5+</div>\n                  <div className=\"text-xs text-text-secondary\">Years Experience</div>\n                </div>\n              </motion.div>\n            </div>\n\n            {/* Decorative Elements */}\n            <motion.div\n              initial={{ opacity: 0, rotate: -180 }}\n              animate={{ opacity: 1, rotate: 0 }}\n              transition={{ duration: 1, delay: 1 }}\n              className=\"absolute -top-4 -left-4 w-24 h-24 bg-gradient-to-br from-rose-gold to-blush-pink rounded-full opacity-20 blur-xl\"\n            ></motion.div>\n            <motion.div\n              initial={{ opacity: 0, rotate: 180 }}\n              animate={{ opacity: 1, rotate: 0 }}\n              transition={{ duration: 1, delay: 1.2 }}\n              className=\"absolute -bottom-4 -right-4 w-32 h-32 bg-gradient-to-br from-lavender to-blush-pink rounded-full opacity-20 blur-xl\"\n            ></motion.div>\n          </motion.div>\n        </div>\n      </div>\n    </section>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AAAA;AACA;AATA;;;;;;;;;;AAWe,SAAS;IACtB,MAAM,aAAa,CAAA,GAAA,qIAAA,CAAA,gBAAa,AAAD;IAC/B,MAAM,eAAe,CAAA,GAAA,sHAAA,CAAA,uBAAoB,AAAD,EACtC,WAAW,OAAO,CAAC,QAAQ,EAC3B,WAAW,eAAe;IAG5B,qBACE,6LAAC;QAAQ,WAAU;;0BAEjB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAI,WAAU;;;;;;;;;;;;0BAGjB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG,CAAC;4BAAG;4BAC9B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,UAAU;gCAAK,MAAM;4BAAU;4BAC7C,WAAU;;8CAGV,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAC5B,YAAY;wCAAE,UAAU;wCAAK,OAAO;oCAAI;8CAExC,cAAA,6LAAC,oIAAA,CAAA,QAAK;wCAAC,SAAQ;wCAAY,WAAU;;0DACnC,6LAAC,qMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;4CAA8B;;;;;;;;;;;;8CAMlD,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAC5B,YAAY;wCAAE,UAAU;wCAAK,OAAO;oCAAI;oCACxC,WAAU;;sDAEV,6LAAC;4CAAG,WAAU;;gDAA0F;8DAEtG,6LAAC;oDAAK,WAAU;8DAAoF;;;;;;;;;;;;sDAItG,6LAAC;4CAAE,WAAU;sDAAkE;;;;;;;;;;;;8CAOjF,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAC5B,YAAY;wCAAE,UAAU;wCAAK,OAAO;oCAAI;oCACxC,WAAU;;sDAEV,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,6MAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;8DAClB,6LAAC;8DAAK;;;;;;;;;;;;sDAER,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DACZ;2DAAI,MAAM;qDAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,6LAAC,qMAAA,CAAA,OAAI;4DAAS,WAAU;2DAAb;;;;;;;;;;8DAGf,6LAAC;8DAAK;;;;;;;;;;;;;;;;;;8CAKV,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAC5B,YAAY;wCAAE,UAAU;wCAAK,OAAO;oCAAI;oCACxC,WAAU;;sDAEV,6LAAC,qIAAA,CAAA,SAAM;4CAAC,OAAO;4CAAC,SAAQ;4CAAW,MAAK;4CAAK,WAAU;sDACrD,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAM;gDAAc,QAAO;gDAAS,KAAI;;kEAC5C,6LAAC,uMAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;oDAAiB;kEAElC,6LAAC,qNAAA,CAAA,aAAU;wDAAC,WAAU;;;;;;;;;;;;;;;;;sDAG1B,6LAAC,qIAAA,CAAA,SAAM;4CAAC,OAAO;4CAAC,SAAQ;4CAAU,MAAK;sDACrC,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;0DAAa;;;;;;;;;;;;;;;;;8CAO5B,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAC5B,YAAY;wCAAE,UAAU;wCAAK,OAAO;oCAAI;oCACxC,WAAU;;sDAEV,6LAAC;4CAAE,WAAU;sDAA+B;;;;;;sDAC5C,6LAAC;4CAAI,WAAU;;gDACZ,WAAW,YAAY,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,qBACxC,6LAAC,oIAAA,CAAA,QAAK;wDAAiB,SAAQ;wDAAU,WAAU;kEAChD,KAAK,IAAI;uDADA,KAAK,IAAI;;;;;8DAIvB,6LAAC,oIAAA,CAAA,QAAK;oDAAC,SAAQ;oDAAU,WAAU;8DAAU;;;;;;;;;;;;;;;;;;;;;;;;sCAQnD,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;4BACxC,WAAU;;8CAEV,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,gIAAA,CAAA,UAAK;4CACJ,KAAI;4CACJ,KAAI;4CACJ,IAAI;4CACJ,WAAU;4CACV,QAAQ;;;;;;sDAIV,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4CACT,SAAS;gDAAE,SAAS;gDAAG,OAAO;4CAAE;4CAChC,SAAS;gDAAE,SAAS;gDAAG,OAAO;4CAAE;4CAChC,YAAY;gDAAE,UAAU;gDAAK,OAAO;4CAAI;4CACxC,WAAU;sDAEV,cAAA,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;;;;;kEACf,6LAAC;wDAAK,WAAU;kEAAwC;;;;;;;;;;;;;;;;;sDAI5D,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4CACT,SAAS;gDAAE,SAAS;gDAAG,OAAO;4CAAE;4CAChC,SAAS;gDAAE,SAAS;gDAAG,OAAO;4CAAE;4CAChC,YAAY;gDAAE,UAAU;gDAAK,OAAO;4CAAI;4CACxC,WAAU;sDAEV,cAAA,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEAAsC;;;;;;kEACrD,6LAAC;wDAAI,WAAU;kEAA8B;;;;;;;;;;;;;;;;;;;;;;;8CAMnD,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,SAAS;wCAAG,QAAQ,CAAC;oCAAI;oCACpC,SAAS;wCAAE,SAAS;wCAAG,QAAQ;oCAAE;oCACjC,YAAY;wCAAE,UAAU;wCAAG,OAAO;oCAAE;oCACpC,WAAU;;;;;;8CAEZ,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,SAAS;wCAAG,QAAQ;oCAAI;oCACnC,SAAS;wCAAE,SAAS;wCAAG,QAAQ;oCAAE;oCACjC,YAAY;wCAAE,UAAU;wCAAG,OAAO;oCAAI;oCACtC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOxB;KAnLwB", "debugId": null}}, {"offset": {"line": 632, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/anjali-portfolio/anjali/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"rounded-xl border border-gray-200 bg-white text-text-primary shadow-sm transition-shadow hover:shadow-md\",\n      className\n    )}\n    {...props}\n  />\n))\nCard.displayName = \"Card\"\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"flex flex-col space-y-1.5 p-6\", className)} {...props} />\n))\nCardHeader.displayName = \"CardHeader\"\n\nconst CardTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn(\"font-display text-2xl font-semibold leading-none tracking-tight\", className)}\n    {...props}\n  />\n))\nCardTitle.displayName = \"CardTitle\"\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn(\"text-sm text-text-secondary\", className)}\n    {...props}\n  />\n))\nCardDescription.displayName = \"CardDescription\"\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\n))\nCardContent.displayName = \"CardContent\"\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"flex items-center p-6 pt-0\", className)} {...props} />\n))\nCardFooter.displayName = \"CardFooter\"\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;;;;AAEA,MAAM,qBAAO,6JAAA,CAAA,aAAgB,MAG3B,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,4GACA;QAED,GAAG,KAAK;;;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,6JAAA,CAAA,aAAgB,OAGjC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAAa,GAAG,KAAK;;;;;;;;AAErF,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,6JAAA,CAAA,aAAgB,OAGhC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,mEAAmE;QAChF,GAAG,KAAK;;;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,6JAAA,CAAA,aAAgB,OAGtC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,+BAA+B;QAC5C,GAAG,KAAK;;;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,6JAAA,CAAA,aAAgB,OAGlC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,6JAAA,CAAA,aAAgB,QAGjC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAAa,GAAG,KAAK;;;;;;;;AAElF,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 753, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/anjali-portfolio/anjali/src/components/ui/section.tsx"], "sourcesContent": ["import { cn } from '@/lib/utils'\n\ninterface SectionProps {\n  children: React.ReactNode\n  className?: string\n  id?: string\n  background?: 'default' | 'cream' | 'soft-gray' | 'gradient'\n}\n\nexport function Section({ \n  children, \n  className, \n  id, \n  background = 'default' \n}: SectionProps) {\n  const backgroundClasses = {\n    default: 'bg-white',\n    cream: 'bg-cream',\n    'soft-gray': 'bg-soft-gray',\n    gradient: 'bg-gradient-to-br from-cream to-soft-gray'\n  }\n\n  return (\n    <section \n      id={id}\n      className={cn(\n        'py-16 md:py-24',\n        backgroundClasses[background],\n        className\n      )}\n    >\n      <div className=\"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8\">\n        {children}\n      </div>\n    </section>\n  )\n}\n\ninterface SectionHeaderProps {\n  title: string\n  subtitle?: string\n  description?: string\n  centered?: boolean\n  className?: string\n}\n\nexport function SectionHeader({ \n  title, \n  subtitle, \n  description, \n  centered = true,\n  className \n}: SectionHeaderProps) {\n  return (\n    <div className={cn(\n      'mb-12 md:mb-16',\n      centered && 'text-center',\n      className\n    )}>\n      {subtitle && (\n        <p className=\"text-rose-gold-dark font-medium text-sm uppercase tracking-wide mb-2\">\n          {subtitle}\n        </p>\n      )}\n      <h2 className=\"font-display text-3xl md:text-4xl lg:text-5xl font-bold text-text-primary mb-4\">\n        {title}\n      </h2>\n      {description && (\n        <p className=\"text-text-secondary text-lg max-w-3xl mx-auto leading-relaxed\">\n          {description}\n        </p>\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAAA;;;AASO,SAAS,QAAQ,KAKT;QALS,EACtB,QAAQ,EACR,SAAS,EACT,EAAE,EACF,aAAa,SAAS,EACT,GALS;IAMtB,MAAM,oBAAoB;QACxB,SAAS;QACT,OAAO;QACP,aAAa;QACb,UAAU;IACZ;IAEA,qBACE,6LAAC;QACC,IAAI;QACJ,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kBACA,iBAAiB,CAAC,WAAW,EAC7B;kBAGF,cAAA,6LAAC;YAAI,WAAU;sBACZ;;;;;;;;;;;AAIT;KA3BgB;AAqCT,SAAS,cAAc,KAMT;QANS,EAC5B,KAAK,EACL,QAAQ,EACR,WAAW,EACX,WAAW,IAAI,EACf,SAAS,EACU,GANS;IAO5B,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACf,kBACA,YAAY,eACZ;;YAEC,0BACC,6LAAC;gBAAE,WAAU;0BACV;;;;;;0BAGL,6LAAC;gBAAG,WAAU;0BACX;;;;;;YAEF,6BACC,6LAAC;gBAAE,WAAU;0BACV;;;;;;;;;;;;AAKX;MA5BgB", "debugId": null}}, {"offset": {"line": 836, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/anjali-portfolio/anjali/src/components/ui/animated-element.tsx"], "sourcesContent": ["'use client'\n\nimport { motion } from 'framer-motion'\nimport { cn } from '@/lib/utils'\n\ninterface AnimatedElementProps {\n  children: React.ReactNode\n  className?: string\n  animation?: 'fadeIn' | 'slideUp' | 'slideLeft' | 'slideRight' | 'scale' | 'bounce'\n  delay?: number\n  duration?: number\n  once?: boolean\n}\n\nconst animations = {\n  fadeIn: {\n    initial: { opacity: 0 },\n    animate: { opacity: 1 },\n  },\n  slideUp: {\n    initial: { opacity: 0, y: 50 },\n    animate: { opacity: 1, y: 0 },\n  },\n  slideLeft: {\n    initial: { opacity: 0, x: 50 },\n    animate: { opacity: 1, x: 0 },\n  },\n  slideRight: {\n    initial: { opacity: 0, x: -50 },\n    animate: { opacity: 1, x: 0 },\n  },\n  scale: {\n    initial: { opacity: 0, scale: 0.8 },\n    animate: { opacity: 1, scale: 1 },\n  },\n  bounce: {\n    initial: { opacity: 0, y: -20 },\n    animate: { opacity: 1, y: 0 },\n  },\n}\n\nexport function AnimatedElement({\n  children,\n  className,\n  animation = 'fadeIn',\n  delay = 0,\n  duration = 0.6,\n  once = true,\n}: AnimatedElementProps) {\n  const selectedAnimation = animations[animation]\n\n  return (\n    <motion.div\n      className={cn(className)}\n      initial={selectedAnimation.initial}\n      whileInView={selectedAnimation.animate}\n      viewport={{ once, margin: '-100px' }}\n      transition={{\n        duration,\n        delay,\n        ease: 'easeOut',\n      }}\n    >\n      {children}\n    </motion.div>\n  )\n}\n\ninterface StaggeredContainerProps {\n  children: React.ReactNode\n  className?: string\n  staggerDelay?: number\n}\n\nexport function StaggeredContainer({\n  children,\n  className,\n  staggerDelay = 0.1,\n}: StaggeredContainerProps) {\n  return (\n    <motion.div\n      className={cn(className)}\n      initial=\"hidden\"\n      whileInView=\"visible\"\n      viewport={{ once: true, margin: '-100px' }}\n      variants={{\n        hidden: {},\n        visible: {\n          transition: {\n            staggerChildren: staggerDelay,\n          },\n        },\n      }}\n    >\n      {children}\n    </motion.div>\n  )\n}\n\ninterface StaggeredItemProps {\n  children: React.ReactNode\n  className?: string\n  animation?: 'fadeIn' | 'slideUp' | 'slideLeft' | 'slideRight' | 'scale'\n}\n\nexport function StaggeredItem({\n  children,\n  className,\n  animation = 'slideUp',\n}: StaggeredItemProps) {\n  const selectedAnimation = animations[animation]\n\n  return (\n    <motion.div\n      className={cn(className)}\n      variants={{\n        hidden: selectedAnimation.initial,\n        visible: selectedAnimation.animate,\n      }}\n      transition={{ duration: 0.6, ease: 'easeOut' }}\n    >\n      {children}\n    </motion.div>\n  )\n}\n"], "names": [], "mappings": ";;;;;;AAEA;AACA;AAHA;;;;AAcA,MAAM,aAAa;IACjB,QAAQ;QACN,SAAS;YAAE,SAAS;QAAE;QACtB,SAAS;YAAE,SAAS;QAAE;IACxB;IACA,SAAS;QACP,SAAS;YAAE,SAAS;YAAG,GAAG;QAAG;QAC7B,SAAS;YAAE,SAAS;YAAG,GAAG;QAAE;IAC9B;IACA,WAAW;QACT,SAAS;YAAE,SAAS;YAAG,GAAG;QAAG;QAC7B,SAAS;YAAE,SAAS;YAAG,GAAG;QAAE;IAC9B;IACA,YAAY;QACV,SAAS;YAAE,SAAS;YAAG,GAAG,CAAC;QAAG;QAC9B,SAAS;YAAE,SAAS;YAAG,GAAG;QAAE;IAC9B;IACA,OAAO;QACL,SAAS;YAAE,SAAS;YAAG,OAAO;QAAI;QAClC,SAAS;YAAE,SAAS;YAAG,OAAO;QAAE;IAClC;IACA,QAAQ;QACN,SAAS;YAAE,SAAS;YAAG,GAAG,CAAC;QAAG;QAC9B,SAAS;YAAE,SAAS;YAAG,GAAG;QAAE;IAC9B;AACF;AAEO,SAAS,gBAAgB,KAOT;QAPS,EAC9B,QAAQ,EACR,SAAS,EACT,YAAY,QAAQ,EACpB,QAAQ,CAAC,EACT,WAAW,GAAG,EACd,OAAO,IAAI,EACU,GAPS;IAQ9B,MAAM,oBAAoB,UAAU,CAAC,UAAU;IAE/C,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;QACT,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE;QACd,SAAS,kBAAkB,OAAO;QAClC,aAAa,kBAAkB,OAAO;QACtC,UAAU;YAAE;YAAM,QAAQ;QAAS;QACnC,YAAY;YACV;YACA;YACA,MAAM;QACR;kBAEC;;;;;;AAGP;KAzBgB;AAiCT,SAAS,mBAAmB,KAIT;QAJS,EACjC,QAAQ,EACR,SAAS,EACT,eAAe,GAAG,EACM,GAJS;IAKjC,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;QACT,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE;QACd,SAAQ;QACR,aAAY;QACZ,UAAU;YAAE,MAAM;YAAM,QAAQ;QAAS;QACzC,UAAU;YACR,QAAQ,CAAC;YACT,SAAS;gBACP,YAAY;oBACV,iBAAiB;gBACnB;YACF;QACF;kBAEC;;;;;;AAGP;MAvBgB;AA+BT,SAAS,cAAc,KAIT;QAJS,EAC5B,QAAQ,EACR,SAAS,EACT,YAAY,SAAS,EACF,GAJS;IAK5B,MAAM,oBAAoB,UAAU,CAAC,UAAU;IAE/C,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;QACT,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE;QACd,UAAU;YACR,QAAQ,kBAAkB,OAAO;YACjC,SAAS,kBAAkB,OAAO;QACpC;QACA,YAAY;YAAE,UAAU;YAAK,MAAM;QAAU;kBAE5C;;;;;;AAGP;MAnBgB", "debugId": null}}, {"offset": {"line": 992, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/anjali-portfolio/anjali/src/components/sections/services-overview.tsx"], "sourcesContent": ["'use client'\n\nimport Link from 'next/link'\nimport Image from 'next/image'\nimport { <PERSON><PERSON><PERSON>, <PERSON>, Star } from 'lucide-react'\nimport { <PERSON><PERSON> } from '@/components/ui/button'\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Badge } from '@/components/ui/badge'\nimport { Section, SectionHeader } from '@/components/ui/section'\nimport { AnimatedElement, StaggeredContainer, StaggeredItem } from '@/components/ui/animated-element'\nimport { getPopularServices } from '@/lib/data'\nimport { formatPrice } from '@/lib/utils'\n\nexport default function ServicesOverview() {\n  const popularServices = getPopularServices()\n\n  return (\n    <Section background=\"cream\" id=\"services\">\n      <AnimatedElement animation=\"fadeIn\">\n        <SectionHeader\n          subtitle=\"Our Services\"\n          title=\"Professional Makeup Services\"\n          description=\"From bridal elegance to party glamour, we offer comprehensive makeup services tailored to your unique style and occasion.\"\n        />\n      </AnimatedElement>\n\n      <StaggeredContainer className=\"grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12\">\n        {popularServices.map((service, index) => (\n          <StaggeredItem key={service.id}>\n            <Card className=\"group hover:shadow-xl transition-all duration-300 border-0 bg-white/80 backdrop-blur-sm\">\n              <div className=\"relative aspect-[4/3] overflow-hidden rounded-t-xl\">\n                <Image\n                  src={`https://images.unsplash.com/photo-1596462502278-27bfdc403348?w=400&h=300&fit=crop&crop=face&q=80`}\n                  alt={service.title}\n                  fill\n                  className=\"object-cover group-hover:scale-105 transition-transform duration-300\"\n                />\n                {service.popular && (\n                  <Badge className=\"absolute top-3 left-3\" variant=\"default\">\n                    <Star className=\"w-3 h-3 mr-1 fill-current\" />\n                    Popular\n                  </Badge>\n                )}\n              </div>\n              \n              <CardHeader className=\"pb-3\">\n                <CardTitle className=\"text-xl group-hover:text-rose-gold-dark transition-colors\">\n                  {service.title}\n                </CardTitle>\n                <CardDescription className=\"text-text-secondary\">\n                  {service.description}\n                </CardDescription>\n              </CardHeader>\n              \n              <CardContent className=\"space-y-4\">\n                <div className=\"flex items-center justify-between text-sm\">\n                  <div className=\"flex items-center gap-2 text-text-secondary\">\n                    <Clock className=\"w-4 h-4\" />\n                    <span>{service.duration}</span>\n                  </div>\n                  <div className=\"font-semibold text-rose-gold-dark\">\n                    {formatPrice(service.price)}\n                  </div>\n                </div>\n                \n                <ul className=\"space-y-1\">\n                  {service.features.slice(0, 3).map((feature, idx) => (\n                    <li key={idx} className=\"text-sm text-text-secondary flex items-center gap-2\">\n                      <div className=\"w-1.5 h-1.5 bg-rose-gold rounded-full flex-shrink-0\"></div>\n                      {feature}\n                    </li>\n                  ))}\n                  {service.features.length > 3 && (\n                    <li className=\"text-sm text-text-muted\">\n                      +{service.features.length - 3} more features\n                    </li>\n                  )}\n                </ul>\n                \n                <Button asChild variant=\"outline\" className=\"w-full group\">\n                  <Link href={`/services#${service.id}`}>\n                    Learn More\n                    <ArrowRight className=\"w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform\" />\n                  </Link>\n                </Button>\n              </CardContent>\n            </Card>\n          </StaggeredItem>\n        ))}\n      </StaggeredContainer>\n\n      <AnimatedElement animation=\"slideUp\" delay={0.6} className=\"text-center\">\n        <Button asChild variant=\"gradient\" size=\"lg\">\n          <Link href=\"/services\">\n            View All Services\n            <ArrowRight className=\"w-5 h-5 ml-2\" />\n          </Link>\n        </Button>\n      </AnimatedElement>\n    </Section>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AAXA;;;;;;;;;;;;AAae,SAAS;IACtB,MAAM,kBAAkB,CAAA,GAAA,qIAAA,CAAA,qBAAkB,AAAD;IAEzC,qBACE,6LAAC,sIAAA,CAAA,UAAO;QAAC,YAAW;QAAQ,IAAG;;0BAC7B,6LAAC,kJAAA,CAAA,kBAAe;gBAAC,WAAU;0BACzB,cAAA,6LAAC,sIAAA,CAAA,gBAAa;oBACZ,UAAS;oBACT,OAAM;oBACN,aAAY;;;;;;;;;;;0BAIhB,6LAAC,kJAAA,CAAA,qBAAkB;gBAAC,WAAU;0BAC3B,gBAAgB,GAAG,CAAC,CAAC,SAAS,sBAC7B,6LAAC,kJAAA,CAAA,gBAAa;kCACZ,cAAA,6LAAC,mIAAA,CAAA,OAAI;4BAAC,WAAU;;8CACd,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,gIAAA,CAAA,UAAK;4CACJ,KAAM;4CACN,KAAK,QAAQ,KAAK;4CAClB,IAAI;4CACJ,WAAU;;;;;;wCAEX,QAAQ,OAAO,kBACd,6LAAC,oIAAA,CAAA,QAAK;4CAAC,WAAU;4CAAwB,SAAQ;;8DAC/C,6LAAC,qMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;gDAA8B;;;;;;;;;;;;;8CAMpD,6LAAC,mIAAA,CAAA,aAAU;oCAAC,WAAU;;sDACpB,6LAAC,mIAAA,CAAA,YAAS;4CAAC,WAAU;sDAClB,QAAQ,KAAK;;;;;;sDAEhB,6LAAC,mIAAA,CAAA,kBAAe;4CAAC,WAAU;sDACxB,QAAQ,WAAW;;;;;;;;;;;;8CAIxB,6LAAC,mIAAA,CAAA,cAAW;oCAAC,WAAU;;sDACrB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,uMAAA,CAAA,QAAK;4DAAC,WAAU;;;;;;sEACjB,6LAAC;sEAAM,QAAQ,QAAQ;;;;;;;;;;;;8DAEzB,6LAAC;oDAAI,WAAU;8DACZ,CAAA,GAAA,sHAAA,CAAA,cAAW,AAAD,EAAE,QAAQ,KAAK;;;;;;;;;;;;sDAI9B,6LAAC;4CAAG,WAAU;;gDACX,QAAQ,QAAQ,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,SAAS,oBAC1C,6LAAC;wDAAa,WAAU;;0EACtB,6LAAC;gEAAI,WAAU;;;;;;4DACd;;uDAFM;;;;;gDAKV,QAAQ,QAAQ,CAAC,MAAM,GAAG,mBACzB,6LAAC;oDAAG,WAAU;;wDAA0B;wDACpC,QAAQ,QAAQ,CAAC,MAAM,GAAG;wDAAE;;;;;;;;;;;;;sDAKpC,6LAAC,qIAAA,CAAA,SAAM;4CAAC,OAAO;4CAAC,SAAQ;4CAAU,WAAU;sDAC1C,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAM,AAAC,aAAuB,OAAX,QAAQ,EAAE;;oDAAI;kEAErC,6LAAC,qNAAA,CAAA,aAAU;wDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;uBAtDZ,QAAQ,EAAE;;;;;;;;;;0BA+DlC,6LAAC,kJAAA,CAAA,kBAAe;gBAAC,WAAU;gBAAU,OAAO;gBAAK,WAAU;0BACzD,cAAA,6LAAC,qIAAA,CAAA,SAAM;oBAAC,OAAO;oBAAC,SAAQ;oBAAW,MAAK;8BACtC,cAAA,6LAAC,+JAAA,CAAA,UAAI;wBAAC,MAAK;;4BAAY;0CAErB,6LAAC,qNAAA,CAAA,aAAU;gCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMlC;KAxFwB", "debugId": null}}, {"offset": {"line": 1294, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/anjali-portfolio/anjali/src/lib/api-client.ts"], "sourcesContent": ["// API client configuration for connecting to the CMS backend\nconst API_BASE_URL = process.env.NEXT_PUBLIC_CMS_API_URL || 'http://localhost:3001/api'\n\nexport class ApiError extends Error {\n  constructor(\n    message: string,\n    public status: number,\n    public data?: any\n  ) {\n    super(message)\n    this.name = 'ApiError'\n  }\n}\n\nasync function apiRequest<T>(\n  endpoint: string,\n  options: RequestInit = {}\n): Promise<T> {\n  const url = `${API_BASE_URL}${endpoint}`\n  \n  const config: RequestInit = {\n    headers: {\n      'Content-Type': 'application/json',\n      ...options.headers,\n    },\n    ...options,\n  }\n\n  try {\n    const response = await fetch(url, config)\n    \n    if (!response.ok) {\n      let errorData\n      try {\n        errorData = await response.json()\n      } catch {\n        errorData = { message: response.statusText }\n      }\n      \n      throw new ApiError(\n        errorData.error || errorData.message || 'An error occurred',\n        response.status,\n        errorData\n      )\n    }\n\n    // Handle empty responses\n    if (response.status === 204) {\n      return {} as T\n    }\n\n    return await response.json()\n  } catch (error) {\n    if (error instanceof ApiError) {\n      throw error\n    }\n    \n    // Network or other errors\n    throw new ApiError(\n      'Network error or server unavailable',\n      0,\n      { originalError: error }\n    )\n  }\n}\n\nexport const api = {\n  get: <T>(endpoint: string) => apiRequest<T>(endpoint),\n  post: <T>(endpoint: string, data?: any) =>\n    apiRequest<T>(endpoint, {\n      method: 'POST',\n      body: data ? JSON.stringify(data) : undefined,\n    }),\n  put: <T>(endpoint: string, data?: any) =>\n    apiRequest<T>(endpoint, {\n      method: 'PUT',\n      body: data ? JSON.stringify(data) : undefined,\n    }),\n  delete: <T>(endpoint: string) =>\n    apiRequest<T>(endpoint, { method: 'DELETE' }),\n}\n\n// Query parameter builder\nexport function buildQueryParams(params: Record<string, any>): string {\n  const searchParams = new URLSearchParams()\n  \n  Object.entries(params).forEach(([key, value]) => {\n    if (value !== undefined && value !== null && value !== '') {\n      searchParams.append(key, String(value))\n    }\n  })\n  \n  const queryString = searchParams.toString()\n  return queryString ? `?${queryString}` : ''\n}\n"], "names": [], "mappings": "AAAA,6DAA6D;;;;;;AACxC;;;AAArB,MAAM,eAAe,iEAAuC;AAErD,MAAM,iBAAiB;IAC5B,YACE,OAAe,EACf,AAAO,MAAc,EACrB,AAAO,IAAU,CACjB;QACA,KAAK,CAAC,iZAHC,SAAA,aACA,OAAA;QAGP,IAAI,CAAC,IAAI,GAAG;IACd;AACF;AAEA,eAAe,WACb,QAAgB;QAChB,UAAA,iEAAuB,CAAC;IAExB,MAAM,MAAM,AAAC,GAAiB,OAAf,cAAwB,OAAT;IAE9B,MAAM,SAAsB;QAC1B,SAAS;YACP,gBAAgB;YAChB,GAAG,QAAQ,OAAO;QACpB;QACA,GAAG,OAAO;IACZ;IAEA,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,KAAK;QAElC,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,IAAI;YACJ,IAAI;gBACF,YAAY,MAAM,SAAS,IAAI;YACjC,EAAE,UAAM;gBACN,YAAY;oBAAE,SAAS,SAAS,UAAU;gBAAC;YAC7C;YAEA,MAAM,IAAI,SACR,UAAU,KAAK,IAAI,UAAU,OAAO,IAAI,qBACxC,SAAS,MAAM,EACf;QAEJ;QAEA,yBAAyB;QACzB,IAAI,SAAS,MAAM,KAAK,KAAK;YAC3B,OAAO,CAAC;QACV;QAEA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,IAAI,iBAAiB,UAAU;YAC7B,MAAM;QACR;QAEA,0BAA0B;QAC1B,MAAM,IAAI,SACR,uCACA,GACA;YAAE,eAAe;QAAM;IAE3B;AACF;AAEO,MAAM,MAAM;IACjB,KAAK,CAAI,WAAqB,WAAc;IAC5C,MAAM,CAAI,UAAkB,OAC1B,WAAc,UAAU;YACtB,QAAQ;YACR,MAAM,OAAO,KAAK,SAAS,CAAC,QAAQ;QACtC;IACF,KAAK,CAAI,UAAkB,OACzB,WAAc,UAAU;YACtB,QAAQ;YACR,MAAM,OAAO,KAAK,SAAS,CAAC,QAAQ;QACtC;IACF,QAAQ,CAAI,WACV,WAAc,UAAU;YAAE,QAAQ;QAAS;AAC/C;AAGO,SAAS,iBAAiB,MAA2B;IAC1D,MAAM,eAAe,IAAI;IAEzB,OAAO,OAAO,CAAC,QAAQ,OAAO,CAAC;YAAC,CAAC,KAAK,MAAM;QAC1C,IAAI,UAAU,aAAa,UAAU,QAAQ,UAAU,IAAI;YACzD,aAAa,MAAM,CAAC,KAAK,OAAO;QAClC;IACF;IAEA,MAAM,cAAc,aAAa,QAAQ;IACzC,OAAO,cAAc,AAAC,IAAe,OAAZ,eAAgB;AAC3C", "debugId": null}}, {"offset": {"line": 1382, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/anjali-portfolio/anjali/src/services/api.ts"], "sourcesContent": ["import { api, buildQueryParams } from '@/lib/api-client'\nimport type {\n  Blog,\n  BlogsResponse,\n  BlogQueryParams,\n  Service,\n  ServicesResponse,\n  ServiceQueryParams,\n  Package,\n  PackagesResponse,\n  PackageQueryParams,\n  Testimonial,\n  TestimonialsResponse,\n  TestimonialQueryParams,\n  GalleryItem,\n  GalleryResponse,\n  GalleryQueryParams,\n  SiteSetting,\n  SettingsResponse,\n} from '@/types/api'\n\n// Blog API functions\nexport const blogApi = {\n  getBlogs: async (params: BlogQueryParams = {}): Promise<BlogsResponse> => {\n    const queryString = buildQueryParams(params)\n    return api.get<BlogsResponse>(`/blogs${queryString}`)\n  },\n\n  getBlog: async (id: string): Promise<Blog> => {\n    return api.get<Blog>(`/blogs/${id}`)\n  },\n\n  getBlogBySlug: async (slug: string): Promise<Blog> => {\n    return api.get<Blog>(`/blogs/slug/${slug}`)\n  },\n\n  getFeaturedBlogs: async (limit = 3): Promise<BlogsResponse> => {\n    return api.get<BlogsResponse>(`/blogs?featured=true&limit=${limit}&status=PUBLISHED`)\n  },\n\n  getRecentBlogs: async (limit = 5): Promise<BlogsResponse> => {\n    return api.get<BlogsResponse>(`/blogs?limit=${limit}&status=PUBLISHED`)\n  },\n}\n\n// Service API functions\nexport const serviceApi = {\n  getServices: async (params: ServiceQueryParams = {}): Promise<ServicesResponse> => {\n    const queryString = buildQueryParams(params)\n    return api.get<ServicesResponse>(`/services${queryString}`)\n  },\n\n  getService: async (id: string): Promise<Service> => {\n    return api.get<Service>(`/services/${id}`)\n  },\n\n  getServiceBySlug: async (slug: string): Promise<Service> => {\n    return api.get<Service>(`/services/slug/${slug}`)\n  },\n\n  getActiveServices: async (): Promise<ServicesResponse> => {\n    return api.get<ServicesResponse>('/services?status=ACTIVE')\n  },\n\n  getPopularServices: async (limit = 6): Promise<ServicesResponse> => {\n    return api.get<ServicesResponse>(`/services?popular=true&status=ACTIVE&limit=${limit}`)\n  },\n\n  getServicesByCategory: async (category: string): Promise<ServicesResponse> => {\n    return api.get<ServicesResponse>(`/services?category=${encodeURIComponent(category)}&status=ACTIVE`)\n  },\n}\n\n// Package API functions\nexport const packageApi = {\n  getPackages: async (params: PackageQueryParams = {}): Promise<PackagesResponse> => {\n    const queryString = buildQueryParams(params)\n    return api.get<PackagesResponse>(`/packages${queryString}`)\n  },\n\n  getPackage: async (id: string): Promise<Package> => {\n    return api.get<Package>(`/packages/${id}`)\n  },\n\n  getPackageBySlug: async (slug: string): Promise<Package> => {\n    return api.get<Package>(`/packages/slug/${slug}`)\n  },\n\n  getActivePackages: async (): Promise<PackagesResponse> => {\n    return api.get<PackagesResponse>('/packages?status=ACTIVE')\n  },\n\n  getPopularPackages: async (limit = 3): Promise<PackagesResponse> => {\n    return api.get<PackagesResponse>(`/packages?popular=true&status=ACTIVE&limit=${limit}`)\n  },\n\n  getFeaturedPackages: async (): Promise<PackagesResponse> => {\n    return api.get<PackagesResponse>('/packages?popular=true&status=ACTIVE')\n  },\n}\n\n// Testimonial API functions\nexport const testimonialApi = {\n  getTestimonials: async (params: TestimonialQueryParams = {}): Promise<TestimonialsResponse> => {\n    const queryString = buildQueryParams(params)\n    return api.get<TestimonialsResponse>(`/testimonials${queryString}`)\n  },\n\n  getTestimonial: async (id: string): Promise<Testimonial> => {\n    return api.get<Testimonial>(`/testimonials/${id}`)\n  },\n\n  getApprovedTestimonials: async (limit?: number): Promise<TestimonialsResponse> => {\n    const params = { status: 'APPROVED', ...(limit && { limit }) }\n    const queryString = buildQueryParams(params)\n    return api.get<TestimonialsResponse>(`/testimonials${queryString}`)\n  },\n\n  getTestimonialsByService: async (service: string): Promise<TestimonialsResponse> => {\n    return api.get<TestimonialsResponse>(`/testimonials?service=${encodeURIComponent(service)}&status=APPROVED`)\n  },\n\n  createTestimonial: async (data: Omit<Testimonial, 'id' | 'status' | 'createdAt' | 'updatedAt'>): Promise<Testimonial> => {\n    return api.post<Testimonial>('/testimonials', data)\n  },\n}\n\n// Gallery API functions\nexport const galleryApi = {\n  getGallery: async (params: GalleryQueryParams = {}): Promise<GalleryResponse> => {\n    const queryString = buildQueryParams(params)\n    return api.get<GalleryResponse>(`/gallery${queryString}`)\n  },\n\n  getGalleryItem: async (id: string): Promise<GalleryItem> => {\n    return api.get<GalleryItem>(`/gallery/${id}`)\n  },\n\n  getActiveGallery: async (limit?: number): Promise<GalleryResponse> => {\n    const params = { status: 'ACTIVE', ...(limit && { limit }) }\n    const queryString = buildQueryParams(params)\n    return api.get<GalleryResponse>(`/gallery${queryString}`)\n  },\n\n  getFeaturedGallery: async (limit = 12): Promise<GalleryResponse> => {\n    return api.get<GalleryResponse>(`/gallery?featured=true&status=ACTIVE&limit=${limit}`)\n  },\n\n  getGalleryByCategory: async (category: string): Promise<GalleryResponse> => {\n    return api.get<GalleryResponse>(`/gallery?category=${encodeURIComponent(category)}&status=ACTIVE`)\n  },\n}\n\n// Settings API functions\nexport const settingsApi = {\n  getSettings: async (): Promise<SettingsResponse> => {\n    return api.get<SettingsResponse>('/settings')\n  },\n\n  getSetting: async (key: string): Promise<SiteSetting> => {\n    return api.get<SiteSetting>(`/settings/${key}`)\n  },\n\n  getSettingValue: async (key: string): Promise<string> => {\n    const setting = await api.get<SiteSetting>(`/settings/${key}`)\n    return setting.value\n  },\n\n  // Helper functions for common settings\n  getSiteInfo: async () => {\n    const settings = await settingsApi.getSettings()\n    const siteSettings = settings.grouped?.site || []\n    \n    return {\n      name: siteSettings.find(s => s.key === 'site.name')?.value || 'Anjali Makeup Artist',\n      tagline: siteSettings.find(s => s.key === 'site.tagline')?.value || 'Enhancing Natural Beauty',\n      description: siteSettings.find(s => s.key === 'site.description')?.value || '',\n      logo: siteSettings.find(s => s.key === 'site.logo')?.value,\n      favicon: siteSettings.find(s => s.key === 'site.favicon')?.value,\n    }\n  },\n\n  getContactInfo: async () => {\n    const settings = await settingsApi.getSettings()\n    const contactSettings = settings.grouped?.contact || []\n    \n    return {\n      email: contactSettings.find(s => s.key === 'contact.email')?.value,\n      phone: contactSettings.find(s => s.key === 'contact.phone')?.value,\n      address: contactSettings.find(s => s.key === 'contact.address')?.value,\n      city: contactSettings.find(s => s.key === 'contact.city')?.value,\n      state: contactSettings.find(s => s.key === 'contact.state')?.value,\n      zipcode: contactSettings.find(s => s.key === 'contact.zipcode')?.value,\n      country: contactSettings.find(s => s.key === 'contact.country')?.value,\n    }\n  },\n\n  getSocialMedia: async () => {\n    const settings = await settingsApi.getSettings()\n    const socialSettings = settings.grouped?.social || []\n    \n    return {\n      facebook: socialSettings.find(s => s.key === 'social.facebook')?.value,\n      instagram: socialSettings.find(s => s.key === 'social.instagram')?.value,\n      twitter: socialSettings.find(s => s.key === 'social.twitter')?.value,\n      youtube: socialSettings.find(s => s.key === 'social.youtube')?.value,\n      linkedin: socialSettings.find(s => s.key === 'social.linkedin')?.value,\n      tiktok: socialSettings.find(s => s.key === 'social.tiktok')?.value,\n    }\n  },\n\n  getSEODefaults: async () => {\n    const settings = await settingsApi.getSettings()\n    const seoSettings = settings.grouped?.seo || []\n    \n    return {\n      metaTitle: seoSettings.find(s => s.key === 'seo.meta_title')?.value,\n      metaDescription: seoSettings.find(s => s.key === 'seo.meta_description')?.value,\n      keywords: seoSettings.find(s => s.key === 'seo.keywords')?.value,\n      ogImage: seoSettings.find(s => s.key === 'seo.og_image')?.value,\n    }\n  },\n\n  getBusinessInfo: async () => {\n    const settings = await settingsApi.getSettings()\n    const businessSettings = settings.grouped?.business || []\n    \n    return {\n      hours: businessSettings.find(s => s.key === 'business.hours')?.value,\n      servicesIntro: businessSettings.find(s => s.key === 'business.services_intro')?.value,\n      aboutText: businessSettings.find(s => s.key === 'business.about_text')?.value,\n      bookingUrl: businessSettings.find(s => s.key === 'business.booking_url')?.value,\n    }\n  },\n}\n\n// Combined API object\nexport const cmsApi = {\n  blogs: blogApi,\n  services: serviceApi,\n  packages: packageApi,\n  testimonials: testimonialApi,\n  gallery: galleryApi,\n  settings: settingsApi,\n}\n"], "names": [], "mappings": ";;;;;;;;;AAAA;;AAsBO,MAAM,UAAU;IACrB,UAAU;YAAO,0EAA0B,CAAC;QAC1C,MAAM,cAAc,CAAA,GAAA,8HAAA,CAAA,mBAAgB,AAAD,EAAE;QACrC,OAAO,8HAAA,CAAA,MAAG,CAAC,GAAG,CAAgB,AAAC,SAAoB,OAAZ;IACzC;IAEA,SAAS,OAAO;QACd,OAAO,8HAAA,CAAA,MAAG,CAAC,GAAG,CAAO,AAAC,UAAY,OAAH;IACjC;IAEA,eAAe,OAAO;QACpB,OAAO,8HAAA,CAAA,MAAG,CAAC,GAAG,CAAO,AAAC,eAAmB,OAAL;IACtC;IAEA,kBAAkB;YAAO,yEAAQ;QAC/B,OAAO,8HAAA,CAAA,MAAG,CAAC,GAAG,CAAgB,AAAC,8BAAmC,OAAN,OAAM;IACpE;IAEA,gBAAgB;YAAO,yEAAQ;QAC7B,OAAO,8HAAA,CAAA,MAAG,CAAC,GAAG,CAAgB,AAAC,gBAAqB,OAAN,OAAM;IACtD;AACF;AAGO,MAAM,aAAa;IACxB,aAAa;YAAO,0EAA6B,CAAC;QAChD,MAAM,cAAc,CAAA,GAAA,8HAAA,CAAA,mBAAgB,AAAD,EAAE;QACrC,OAAO,8HAAA,CAAA,MAAG,CAAC,GAAG,CAAmB,AAAC,YAAuB,OAAZ;IAC/C;IAEA,YAAY,OAAO;QACjB,OAAO,8HAAA,CAAA,MAAG,CAAC,GAAG,CAAU,AAAC,aAAe,OAAH;IACvC;IAEA,kBAAkB,OAAO;QACvB,OAAO,8HAAA,CAAA,MAAG,CAAC,GAAG,CAAU,AAAC,kBAAsB,OAAL;IAC5C;IAEA,mBAAmB;QACjB,OAAO,8HAAA,CAAA,MAAG,CAAC,GAAG,CAAmB;IACnC;IAEA,oBAAoB;YAAO,yEAAQ;QACjC,OAAO,8HAAA,CAAA,MAAG,CAAC,GAAG,CAAmB,AAAC,8CAAmD,OAAN;IACjF;IAEA,uBAAuB,OAAO;QAC5B,OAAO,8HAAA,CAAA,MAAG,CAAC,GAAG,CAAmB,AAAC,sBAAkD,OAA7B,mBAAmB,WAAU;IACtF;AACF;AAGO,MAAM,aAAa;IACxB,aAAa;YAAO,0EAA6B,CAAC;QAChD,MAAM,cAAc,CAAA,GAAA,8HAAA,CAAA,mBAAgB,AAAD,EAAE;QACrC,OAAO,8HAAA,CAAA,MAAG,CAAC,GAAG,CAAmB,AAAC,YAAuB,OAAZ;IAC/C;IAEA,YAAY,OAAO;QACjB,OAAO,8HAAA,CAAA,MAAG,CAAC,GAAG,CAAU,AAAC,aAAe,OAAH;IACvC;IAEA,kBAAkB,OAAO;QACvB,OAAO,8HAAA,CAAA,MAAG,CAAC,GAAG,CAAU,AAAC,kBAAsB,OAAL;IAC5C;IAEA,mBAAmB;QACjB,OAAO,8HAAA,CAAA,MAAG,CAAC,GAAG,CAAmB;IACnC;IAEA,oBAAoB;YAAO,yEAAQ;QACjC,OAAO,8HAAA,CAAA,MAAG,CAAC,GAAG,CAAmB,AAAC,8CAAmD,OAAN;IACjF;IAEA,qBAAqB;QACnB,OAAO,8HAAA,CAAA,MAAG,CAAC,GAAG,CAAmB;IACnC;AACF;AAGO,MAAM,iBAAiB;IAC5B,iBAAiB;YAAO,0EAAiC,CAAC;QACxD,MAAM,cAAc,CAAA,GAAA,8HAAA,CAAA,mBAAgB,AAAD,EAAE;QACrC,OAAO,8HAAA,CAAA,MAAG,CAAC,GAAG,CAAuB,AAAC,gBAA2B,OAAZ;IACvD;IAEA,gBAAgB,OAAO;QACrB,OAAO,8HAAA,CAAA,MAAG,CAAC,GAAG,CAAc,AAAC,iBAAmB,OAAH;IAC/C;IAEA,yBAAyB,OAAO;QAC9B,MAAM,SAAS;YAAE,QAAQ;YAAY,GAAI,SAAS;gBAAE;YAAM,CAAC;QAAE;QAC7D,MAAM,cAAc,CAAA,GAAA,8HAAA,CAAA,mBAAgB,AAAD,EAAE;QACrC,OAAO,8HAAA,CAAA,MAAG,CAAC,GAAG,CAAuB,AAAC,gBAA2B,OAAZ;IACvD;IAEA,0BAA0B,OAAO;QAC/B,OAAO,8HAAA,CAAA,MAAG,CAAC,GAAG,CAAuB,AAAC,yBAAoD,OAA5B,mBAAmB,UAAS;IAC5F;IAEA,mBAAmB,OAAO;QACxB,OAAO,8HAAA,CAAA,MAAG,CAAC,IAAI,CAAc,iBAAiB;IAChD;AACF;AAGO,MAAM,aAAa;IACxB,YAAY;YAAO,0EAA6B,CAAC;QAC/C,MAAM,cAAc,CAAA,GAAA,8HAAA,CAAA,mBAAgB,AAAD,EAAE;QACrC,OAAO,8HAAA,CAAA,MAAG,CAAC,GAAG,CAAkB,AAAC,WAAsB,OAAZ;IAC7C;IAEA,gBAAgB,OAAO;QACrB,OAAO,8HAAA,CAAA,MAAG,CAAC,GAAG,CAAc,AAAC,YAAc,OAAH;IAC1C;IAEA,kBAAkB,OAAO;QACvB,MAAM,SAAS;YAAE,QAAQ;YAAU,GAAI,SAAS;gBAAE;YAAM,CAAC;QAAE;QAC3D,MAAM,cAAc,CAAA,GAAA,8HAAA,CAAA,mBAAgB,AAAD,EAAE;QACrC,OAAO,8HAAA,CAAA,MAAG,CAAC,GAAG,CAAkB,AAAC,WAAsB,OAAZ;IAC7C;IAEA,oBAAoB;YAAO,yEAAQ;QACjC,OAAO,8HAAA,CAAA,MAAG,CAAC,GAAG,CAAkB,AAAC,8CAAmD,OAAN;IAChF;IAEA,sBAAsB,OAAO;QAC3B,OAAO,8HAAA,CAAA,MAAG,CAAC,GAAG,CAAkB,AAAC,qBAAiD,OAA7B,mBAAmB,WAAU;IACpF;AACF;AAGO,MAAM,cAAc;IACzB,aAAa;QACX,OAAO,8HAAA,CAAA,MAAG,CAAC,GAAG,CAAmB;IACnC;IAEA,YAAY,OAAO;QACjB,OAAO,8HAAA,CAAA,MAAG,CAAC,GAAG,CAAc,AAAC,aAAgB,OAAJ;IAC3C;IAEA,iBAAiB,OAAO;QACtB,MAAM,UAAU,MAAM,8HAAA,CAAA,MAAG,CAAC,GAAG,CAAc,AAAC,aAAgB,OAAJ;QACxD,OAAO,QAAQ,KAAK;IACtB;IAEA,uCAAuC;IACvC,aAAa;YAEU,mBAGb,oBACG,qBACI,qBACP,qBACG;QARX,MAAM,WAAW,MAAM,YAAY,WAAW;QAC9C,MAAM,eAAe,EAAA,oBAAA,SAAS,OAAO,cAAhB,wCAAA,kBAAkB,IAAI,KAAI,EAAE;QAEjD,OAAO;YACL,MAAM,EAAA,qBAAA,aAAa,IAAI,CAAC,CAAA,IAAK,EAAE,GAAG,KAAK,0BAAjC,yCAAA,mBAA+C,KAAK,KAAI;YAC9D,SAAS,EAAA,sBAAA,aAAa,IAAI,CAAC,CAAA,IAAK,EAAE,GAAG,KAAK,6BAAjC,0CAAA,oBAAkD,KAAK,KAAI;YACpE,aAAa,EAAA,sBAAA,aAAa,IAAI,CAAC,CAAA,IAAK,EAAE,GAAG,KAAK,iCAAjC,0CAAA,oBAAsD,KAAK,KAAI;YAC5E,IAAI,GAAE,sBAAA,aAAa,IAAI,CAAC,CAAA,IAAK,EAAE,GAAG,KAAK,0BAAjC,0CAAA,oBAA+C,KAAK;YAC1D,OAAO,GAAE,sBAAA,aAAa,IAAI,CAAC,CAAA,IAAK,EAAE,GAAG,KAAK,6BAAjC,0CAAA,oBAAkD,KAAK;QAClE;IACF;IAEA,gBAAgB;YAEU,mBAGf,uBACA,wBACE,wBACH,wBACC,wBACE,wBACA;QAVX,MAAM,WAAW,MAAM,YAAY,WAAW;QAC9C,MAAM,kBAAkB,EAAA,oBAAA,SAAS,OAAO,cAAhB,wCAAA,kBAAkB,OAAO,KAAI,EAAE;QAEvD,OAAO;YACL,KAAK,GAAE,wBAAA,gBAAgB,IAAI,CAAC,CAAA,IAAK,EAAE,GAAG,KAAK,8BAApC,4CAAA,sBAAsD,KAAK;YAClE,KAAK,GAAE,yBAAA,gBAAgB,IAAI,CAAC,CAAA,IAAK,EAAE,GAAG,KAAK,8BAApC,6CAAA,uBAAsD,KAAK;YAClE,OAAO,GAAE,yBAAA,gBAAgB,IAAI,CAAC,CAAA,IAAK,EAAE,GAAG,KAAK,gCAApC,6CAAA,uBAAwD,KAAK;YACtE,IAAI,GAAE,yBAAA,gBAAgB,IAAI,CAAC,CAAA,IAAK,EAAE,GAAG,KAAK,6BAApC,6CAAA,uBAAqD,KAAK;YAChE,KAAK,GAAE,yBAAA,gBAAgB,IAAI,CAAC,CAAA,IAAK,EAAE,GAAG,KAAK,8BAApC,6CAAA,uBAAsD,KAAK;YAClE,OAAO,GAAE,yBAAA,gBAAgB,IAAI,CAAC,CAAA,IAAK,EAAE,GAAG,KAAK,gCAApC,6CAAA,uBAAwD,KAAK;YACtE,OAAO,GAAE,yBAAA,gBAAgB,IAAI,CAAC,CAAA,IAAK,EAAE,GAAG,KAAK,gCAApC,6CAAA,uBAAwD,KAAK;QACxE;IACF;IAEA,gBAAgB;YAES,mBAGX,sBACC,uBACF,uBACA,uBACC,uBACF;QATV,MAAM,WAAW,MAAM,YAAY,WAAW;QAC9C,MAAM,iBAAiB,EAAA,oBAAA,SAAS,OAAO,cAAhB,wCAAA,kBAAkB,MAAM,KAAI,EAAE;QAErD,OAAO;YACL,QAAQ,GAAE,uBAAA,eAAe,IAAI,CAAC,CAAA,IAAK,EAAE,GAAG,KAAK,gCAAnC,2CAAA,qBAAuD,KAAK;YACtE,SAAS,GAAE,wBAAA,eAAe,IAAI,CAAC,CAAA,IAAK,EAAE,GAAG,KAAK,iCAAnC,4CAAA,sBAAwD,KAAK;YACxE,OAAO,GAAE,wBAAA,eAAe,IAAI,CAAC,CAAA,IAAK,EAAE,GAAG,KAAK,+BAAnC,4CAAA,sBAAsD,KAAK;YACpE,OAAO,GAAE,wBAAA,eAAe,IAAI,CAAC,CAAA,IAAK,EAAE,GAAG,KAAK,+BAAnC,4CAAA,sBAAsD,KAAK;YACpE,QAAQ,GAAE,wBAAA,eAAe,IAAI,CAAC,CAAA,IAAK,EAAE,GAAG,KAAK,gCAAnC,4CAAA,sBAAuD,KAAK;YACtE,MAAM,GAAE,wBAAA,eAAe,IAAI,CAAC,CAAA,IAAK,EAAE,GAAG,KAAK,8BAAnC,4CAAA,sBAAqD,KAAK;QACpE;IACF;IAEA,gBAAgB;YAEM,mBAGP,mBACM,oBACP,oBACD;QAPX,MAAM,WAAW,MAAM,YAAY,WAAW;QAC9C,MAAM,cAAc,EAAA,oBAAA,SAAS,OAAO,cAAhB,wCAAA,kBAAkB,GAAG,KAAI,EAAE;QAE/C,OAAO;YACL,SAAS,GAAE,oBAAA,YAAY,IAAI,CAAC,CAAA,IAAK,EAAE,GAAG,KAAK,+BAAhC,wCAAA,kBAAmD,KAAK;YACnE,eAAe,GAAE,qBAAA,YAAY,IAAI,CAAC,CAAA,IAAK,EAAE,GAAG,KAAK,qCAAhC,yCAAA,mBAAyD,KAAK;YAC/E,QAAQ,GAAE,qBAAA,YAAY,IAAI,CAAC,CAAA,IAAK,EAAE,GAAG,KAAK,6BAAhC,yCAAA,mBAAiD,KAAK;YAChE,OAAO,GAAE,qBAAA,YAAY,IAAI,CAAC,CAAA,IAAK,EAAE,GAAG,KAAK,6BAAhC,yCAAA,mBAAiD,KAAK;QACjE;IACF;IAEA,iBAAiB;YAEU,mBAGhB,wBACQ,yBACJ,yBACC;QAPd,MAAM,WAAW,MAAM,YAAY,WAAW;QAC9C,MAAM,mBAAmB,EAAA,oBAAA,SAAS,OAAO,cAAhB,wCAAA,kBAAkB,QAAQ,KAAI,EAAE;QAEzD,OAAO;YACL,KAAK,GAAE,yBAAA,iBAAiB,IAAI,CAAC,CAAA,IAAK,EAAE,GAAG,KAAK,+BAArC,6CAAA,uBAAwD,KAAK;YACpE,aAAa,GAAE,0BAAA,iBAAiB,IAAI,CAAC,CAAA,IAAK,EAAE,GAAG,KAAK,wCAArC,8CAAA,wBAAiE,KAAK;YACrF,SAAS,GAAE,0BAAA,iBAAiB,IAAI,CAAC,CAAA,IAAK,EAAE,GAAG,KAAK,oCAArC,8CAAA,wBAA6D,KAAK;YAC7E,UAAU,GAAE,0BAAA,iBAAiB,IAAI,CAAC,CAAA,IAAK,EAAE,GAAG,KAAK,qCAArC,8CAAA,wBAA8D,KAAK;QACjF;IACF;AACF;AAGO,MAAM,SAAS;IACpB,OAAO;IACP,UAAU;IACV,UAAU;IACV,cAAc;IACd,SAAS;IACT,UAAU;AACZ", "debugId": null}}, {"offset": {"line": 1604, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/anjali-portfolio/anjali/src/hooks/use-api.ts"], "sourcesContent": ["import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'\nimport { cmsApi } from '@/services/api'\nimport type {\n  BlogQueryParams,\n  ServiceQueryParams,\n  PackageQueryParams,\n  TestimonialQueryParams,\n  GalleryQueryParams,\n  Testimonial,\n} from '@/types/api'\n\n// Query keys for consistent caching\nexport const queryKeys = {\n  blogs: {\n    all: ['blogs'] as const,\n    lists: () => [...queryKeys.blogs.all, 'list'] as const,\n    list: (params: BlogQueryParams) => [...queryKeys.blogs.lists(), params] as const,\n    details: () => [...queryKeys.blogs.all, 'detail'] as const,\n    detail: (id: string) => [...queryKeys.blogs.details(), id] as const,\n    slug: (slug: string) => [...queryKeys.blogs.all, 'slug', slug] as const,\n    featured: (limit?: number) => [...queryKeys.blogs.all, 'featured', limit] as const,\n    recent: (limit?: number) => [...queryKeys.blogs.all, 'recent', limit] as const,\n  },\n  services: {\n    all: ['services'] as const,\n    lists: () => [...queryKeys.services.all, 'list'] as const,\n    list: (params: ServiceQueryParams) => [...queryKeys.services.lists(), params] as const,\n    details: () => [...queryKeys.services.all, 'detail'] as const,\n    detail: (id: string) => [...queryKeys.services.details(), id] as const,\n    slug: (slug: string) => [...queryKeys.services.all, 'slug', slug] as const,\n    active: () => [...queryKeys.services.all, 'active'] as const,\n    popular: (limit?: number) => [...queryKeys.services.all, 'popular', limit] as const,\n    category: (category: string) => [...queryKeys.services.all, 'category', category] as const,\n  },\n  packages: {\n    all: ['packages'] as const,\n    lists: () => [...queryKeys.packages.all, 'list'] as const,\n    list: (params: PackageQueryParams) => [...queryKeys.packages.lists(), params] as const,\n    details: () => [...queryKeys.packages.all, 'detail'] as const,\n    detail: (id: string) => [...queryKeys.packages.details(), id] as const,\n    slug: (slug: string) => [...queryKeys.packages.all, 'slug', slug] as const,\n    active: () => [...queryKeys.packages.all, 'active'] as const,\n    popular: (limit?: number) => [...queryKeys.packages.all, 'popular', limit] as const,\n    featured: () => [...queryKeys.packages.all, 'featured'] as const,\n  },\n  testimonials: {\n    all: ['testimonials'] as const,\n    lists: () => [...queryKeys.testimonials.all, 'list'] as const,\n    list: (params: TestimonialQueryParams) => [...queryKeys.testimonials.lists(), params] as const,\n    details: () => [...queryKeys.testimonials.all, 'detail'] as const,\n    detail: (id: string) => [...queryKeys.testimonials.details(), id] as const,\n    approved: (limit?: number) => [...queryKeys.testimonials.all, 'approved', limit] as const,\n    service: (service: string) => [...queryKeys.testimonials.all, 'service', service] as const,\n  },\n  gallery: {\n    all: ['gallery'] as const,\n    lists: () => [...queryKeys.gallery.all, 'list'] as const,\n    list: (params: GalleryQueryParams) => [...queryKeys.gallery.lists(), params] as const,\n    details: () => [...queryKeys.gallery.all, 'detail'] as const,\n    detail: (id: string) => [...queryKeys.gallery.details(), id] as const,\n    active: (limit?: number) => [...queryKeys.gallery.all, 'active', limit] as const,\n    featured: (limit?: number) => [...queryKeys.gallery.all, 'featured', limit] as const,\n    category: (category: string) => [...queryKeys.gallery.all, 'category', category] as const,\n  },\n  settings: {\n    all: ['settings'] as const,\n    detail: (key: string) => [...queryKeys.settings.all, key] as const,\n    siteInfo: () => [...queryKeys.settings.all, 'siteInfo'] as const,\n    contactInfo: () => [...queryKeys.settings.all, 'contactInfo'] as const,\n    socialMedia: () => [...queryKeys.settings.all, 'socialMedia'] as const,\n    seoDefaults: () => [...queryKeys.settings.all, 'seoDefaults'] as const,\n    businessInfo: () => [...queryKeys.settings.all, 'businessInfo'] as const,\n  },\n}\n\n// Blog hooks\nexport const useBlogs = (params: BlogQueryParams = {}) => {\n  return useQuery({\n    queryKey: queryKeys.blogs.list(params),\n    queryFn: () => cmsApi.blogs.getBlogs(params),\n    staleTime: 5 * 60 * 1000, // 5 minutes\n  })\n}\n\nexport const useBlog = (id: string) => {\n  return useQuery({\n    queryKey: queryKeys.blogs.detail(id),\n    queryFn: () => cmsApi.blogs.getBlog(id),\n    enabled: !!id,\n  })\n}\n\nexport const useBlogBySlug = (slug: string) => {\n  return useQuery({\n    queryKey: queryKeys.blogs.slug(slug),\n    queryFn: () => cmsApi.blogs.getBlogBySlug(slug),\n    enabled: !!slug,\n  })\n}\n\nexport const useFeaturedBlogs = (limit = 3) => {\n  return useQuery({\n    queryKey: queryKeys.blogs.featured(limit),\n    queryFn: () => cmsApi.blogs.getFeaturedBlogs(limit),\n    staleTime: 10 * 60 * 1000, // 10 minutes\n  })\n}\n\nexport const useRecentBlogs = (limit = 5) => {\n  return useQuery({\n    queryKey: queryKeys.blogs.recent(limit),\n    queryFn: () => cmsApi.blogs.getRecentBlogs(limit),\n    staleTime: 5 * 60 * 1000, // 5 minutes\n  })\n}\n\n// Service hooks\nexport const useServices = (params: ServiceQueryParams = {}) => {\n  return useQuery({\n    queryKey: queryKeys.services.list(params),\n    queryFn: () => cmsApi.services.getServices(params),\n    staleTime: 10 * 60 * 1000, // 10 minutes\n  })\n}\n\nexport const useService = (id: string) => {\n  return useQuery({\n    queryKey: queryKeys.services.detail(id),\n    queryFn: () => cmsApi.services.getService(id),\n    enabled: !!id,\n  })\n}\n\nexport const useServiceBySlug = (slug: string) => {\n  return useQuery({\n    queryKey: queryKeys.services.slug(slug),\n    queryFn: () => cmsApi.services.getServiceBySlug(slug),\n    enabled: !!slug,\n  })\n}\n\nexport const useActiveServices = () => {\n  return useQuery({\n    queryKey: queryKeys.services.active(),\n    queryFn: () => cmsApi.services.getActiveServices(),\n    staleTime: 15 * 60 * 1000, // 15 minutes\n  })\n}\n\nexport const usePopularServices = (limit = 6) => {\n  return useQuery({\n    queryKey: queryKeys.services.popular(limit),\n    queryFn: () => cmsApi.services.getPopularServices(limit),\n    staleTime: 15 * 60 * 1000, // 15 minutes\n  })\n}\n\nexport const useServicesByCategory = (category: string) => {\n  return useQuery({\n    queryKey: queryKeys.services.category(category),\n    queryFn: () => cmsApi.services.getServicesByCategory(category),\n    enabled: !!category,\n    staleTime: 10 * 60 * 1000, // 10 minutes\n  })\n}\n\n// Package hooks\nexport const usePackages = (params: PackageQueryParams = {}) => {\n  return useQuery({\n    queryKey: queryKeys.packages.list(params),\n    queryFn: () => cmsApi.packages.getPackages(params),\n    staleTime: 10 * 60 * 1000, // 10 minutes\n  })\n}\n\nexport const usePackage = (id: string) => {\n  return useQuery({\n    queryKey: queryKeys.packages.detail(id),\n    queryFn: () => cmsApi.packages.getPackage(id),\n    enabled: !!id,\n  })\n}\n\nexport const usePackageBySlug = (slug: string) => {\n  return useQuery({\n    queryKey: queryKeys.packages.slug(slug),\n    queryFn: () => cmsApi.packages.getPackageBySlug(slug),\n    enabled: !!slug,\n  })\n}\n\nexport const useActivePackages = () => {\n  return useQuery({\n    queryKey: queryKeys.packages.active(),\n    queryFn: () => cmsApi.packages.getActivePackages(),\n    staleTime: 15 * 60 * 1000, // 15 minutes\n  })\n}\n\nexport const usePopularPackages = (limit = 3) => {\n  return useQuery({\n    queryKey: queryKeys.packages.popular(limit),\n    queryFn: () => cmsApi.packages.getPopularPackages(limit),\n    staleTime: 15 * 60 * 1000, // 15 minutes\n  })\n}\n\nexport const useFeaturedPackages = () => {\n  return useQuery({\n    queryKey: queryKeys.packages.featured(),\n    queryFn: () => cmsApi.packages.getFeaturedPackages(),\n    staleTime: 15 * 60 * 1000, // 15 minutes\n  })\n}\n\n// Testimonial hooks\nexport const useTestimonials = (params: TestimonialQueryParams = {}) => {\n  return useQuery({\n    queryKey: queryKeys.testimonials.list(params),\n    queryFn: () => cmsApi.testimonials.getTestimonials(params),\n    staleTime: 5 * 60 * 1000, // 5 minutes\n  })\n}\n\nexport const useTestimonial = (id: string) => {\n  return useQuery({\n    queryKey: queryKeys.testimonials.detail(id),\n    queryFn: () => cmsApi.testimonials.getTestimonial(id),\n    enabled: !!id,\n  })\n}\n\nexport const useApprovedTestimonials = (limit?: number) => {\n  return useQuery({\n    queryKey: queryKeys.testimonials.approved(limit),\n    queryFn: () => cmsApi.testimonials.getApprovedTestimonials(limit),\n    staleTime: 10 * 60 * 1000, // 10 minutes\n  })\n}\n\nexport const useTestimonialsByService = (service: string) => {\n  return useQuery({\n    queryKey: queryKeys.testimonials.service(service),\n    queryFn: () => cmsApi.testimonials.getTestimonialsByService(service),\n    enabled: !!service,\n    staleTime: 10 * 60 * 1000, // 10 minutes\n  })\n}\n\n// Testimonial mutation\nexport const useCreateTestimonial = () => {\n  const queryClient = useQueryClient()\n  \n  return useMutation({\n    mutationFn: (data: Omit<Testimonial, 'id' | 'status' | 'createdAt' | 'updatedAt'>) =>\n      cmsApi.testimonials.createTestimonial(data),\n    onSuccess: () => {\n      // Invalidate testimonials queries\n      queryClient.invalidateQueries({ queryKey: queryKeys.testimonials.all })\n    },\n  })\n}\n\n// Gallery hooks\nexport const useGallery = (params: GalleryQueryParams = {}) => {\n  return useQuery({\n    queryKey: queryKeys.gallery.list(params),\n    queryFn: () => cmsApi.gallery.getGallery(params),\n    staleTime: 10 * 60 * 1000, // 10 minutes\n  })\n}\n\nexport const useGalleryItem = (id: string) => {\n  return useQuery({\n    queryKey: queryKeys.gallery.detail(id),\n    queryFn: () => cmsApi.gallery.getGalleryItem(id),\n    enabled: !!id,\n  })\n}\n\nexport const useActiveGallery = (limit?: number) => {\n  return useQuery({\n    queryKey: queryKeys.gallery.active(limit),\n    queryFn: () => cmsApi.gallery.getActiveGallery(limit),\n    staleTime: 15 * 60 * 1000, // 15 minutes\n  })\n}\n\nexport const useFeaturedGallery = (limit = 12) => {\n  return useQuery({\n    queryKey: queryKeys.gallery.featured(limit),\n    queryFn: () => cmsApi.gallery.getFeaturedGallery(limit),\n    staleTime: 15 * 60 * 1000, // 15 minutes\n  })\n}\n\nexport const useGalleryByCategory = (category: string) => {\n  return useQuery({\n    queryKey: queryKeys.gallery.category(category),\n    queryFn: () => cmsApi.gallery.getGalleryByCategory(category),\n    enabled: !!category,\n    staleTime: 10 * 60 * 1000, // 10 minutes\n  })\n}\n\n// Settings hooks\nexport const useSettings = () => {\n  return useQuery({\n    queryKey: queryKeys.settings.all,\n    queryFn: () => cmsApi.settings.getSettings(),\n    staleTime: 30 * 60 * 1000, // 30 minutes\n  })\n}\n\nexport const useSetting = (key: string) => {\n  return useQuery({\n    queryKey: queryKeys.settings.detail(key),\n    queryFn: () => cmsApi.settings.getSetting(key),\n    enabled: !!key,\n    staleTime: 30 * 60 * 1000, // 30 minutes\n  })\n}\n\nexport const useSiteInfo = () => {\n  return useQuery({\n    queryKey: queryKeys.settings.siteInfo(),\n    queryFn: () => cmsApi.settings.getSiteInfo(),\n    staleTime: 60 * 60 * 1000, // 1 hour\n  })\n}\n\nexport const useContactInfo = () => {\n  return useQuery({\n    queryKey: queryKeys.settings.contactInfo(),\n    queryFn: () => cmsApi.settings.getContactInfo(),\n    staleTime: 60 * 60 * 1000, // 1 hour\n  })\n}\n\nexport const useSocialMedia = () => {\n  return useQuery({\n    queryKey: queryKeys.settings.socialMedia(),\n    queryFn: () => cmsApi.settings.getSocialMedia(),\n    staleTime: 60 * 60 * 1000, // 1 hour\n  })\n}\n\nexport const useSEODefaults = () => {\n  return useQuery({\n    queryKey: queryKeys.settings.seoDefaults(),\n    queryFn: () => cmsApi.settings.getSEODefaults(),\n    staleTime: 60 * 60 * 1000, // 1 hour\n  })\n}\n\nexport const useBusinessInfo = () => {\n  return useQuery({\n    queryKey: queryKeys.settings.businessInfo(),\n    queryFn: () => cmsApi.settings.getBusinessInfo(),\n    staleTime: 60 * 60 * 1000, // 1 hour\n  })\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AACA;;;;AAWO,MAAM,YAAY;IACvB,OAAO;QACL,KAAK;YAAC;SAAQ;QACd,OAAO,IAAM;mBAAI,UAAU,KAAK,CAAC,GAAG;gBAAE;aAAO;QAC7C,MAAM,CAAC,SAA4B;mBAAI,UAAU,KAAK,CAAC,KAAK;gBAAI;aAAO;QACvE,SAAS,IAAM;mBAAI,UAAU,KAAK,CAAC,GAAG;gBAAE;aAAS;QACjD,QAAQ,CAAC,KAAe;mBAAI,UAAU,KAAK,CAAC,OAAO;gBAAI;aAAG;QAC1D,MAAM,CAAC,OAAiB;mBAAI,UAAU,KAAK,CAAC,GAAG;gBAAE;gBAAQ;aAAK;QAC9D,UAAU,CAAC,QAAmB;mBAAI,UAAU,KAAK,CAAC,GAAG;gBAAE;gBAAY;aAAM;QACzE,QAAQ,CAAC,QAAmB;mBAAI,UAAU,KAAK,CAAC,GAAG;gBAAE;gBAAU;aAAM;IACvE;IACA,UAAU;QACR,KAAK;YAAC;SAAW;QACjB,OAAO,IAAM;mBAAI,UAAU,QAAQ,CAAC,GAAG;gBAAE;aAAO;QAChD,MAAM,CAAC,SAA+B;mBAAI,UAAU,QAAQ,CAAC,KAAK;gBAAI;aAAO;QAC7E,SAAS,IAAM;mBAAI,UAAU,QAAQ,CAAC,GAAG;gBAAE;aAAS;QACpD,QAAQ,CAAC,KAAe;mBAAI,UAAU,QAAQ,CAAC,OAAO;gBAAI;aAAG;QAC7D,MAAM,CAAC,OAAiB;mBAAI,UAAU,QAAQ,CAAC,GAAG;gBAAE;gBAAQ;aAAK;QACjE,QAAQ,IAAM;mBAAI,UAAU,QAAQ,CAAC,GAAG;gBAAE;aAAS;QACnD,SAAS,CAAC,QAAmB;mBAAI,UAAU,QAAQ,CAAC,GAAG;gBAAE;gBAAW;aAAM;QAC1E,UAAU,CAAC,WAAqB;mBAAI,UAAU,QAAQ,CAAC,GAAG;gBAAE;gBAAY;aAAS;IACnF;IACA,UAAU;QACR,KAAK;YAAC;SAAW;QACjB,OAAO,IAAM;mBAAI,UAAU,QAAQ,CAAC,GAAG;gBAAE;aAAO;QAChD,MAAM,CAAC,SAA+B;mBAAI,UAAU,QAAQ,CAAC,KAAK;gBAAI;aAAO;QAC7E,SAAS,IAAM;mBAAI,UAAU,QAAQ,CAAC,GAAG;gBAAE;aAAS;QACpD,QAAQ,CAAC,KAAe;mBAAI,UAAU,QAAQ,CAAC,OAAO;gBAAI;aAAG;QAC7D,MAAM,CAAC,OAAiB;mBAAI,UAAU,QAAQ,CAAC,GAAG;gBAAE;gBAAQ;aAAK;QACjE,QAAQ,IAAM;mBAAI,UAAU,QAAQ,CAAC,GAAG;gBAAE;aAAS;QACnD,SAAS,CAAC,QAAmB;mBAAI,UAAU,QAAQ,CAAC,GAAG;gBAAE;gBAAW;aAAM;QAC1E,UAAU,IAAM;mBAAI,UAAU,QAAQ,CAAC,GAAG;gBAAE;aAAW;IACzD;IACA,cAAc;QACZ,KAAK;YAAC;SAAe;QACrB,OAAO,IAAM;mBAAI,UAAU,YAAY,CAAC,GAAG;gBAAE;aAAO;QACpD,MAAM,CAAC,SAAmC;mBAAI,UAAU,YAAY,CAAC,KAAK;gBAAI;aAAO;QACrF,SAAS,IAAM;mBAAI,UAAU,YAAY,CAAC,GAAG;gBAAE;aAAS;QACxD,QAAQ,CAAC,KAAe;mBAAI,UAAU,YAAY,CAAC,OAAO;gBAAI;aAAG;QACjE,UAAU,CAAC,QAAmB;mBAAI,UAAU,YAAY,CAAC,GAAG;gBAAE;gBAAY;aAAM;QAChF,SAAS,CAAC,UAAoB;mBAAI,UAAU,YAAY,CAAC,GAAG;gBAAE;gBAAW;aAAQ;IACnF;IACA,SAAS;QACP,KAAK;YAAC;SAAU;QAChB,OAAO,IAAM;mBAAI,UAAU,OAAO,CAAC,GAAG;gBAAE;aAAO;QAC/C,MAAM,CAAC,SAA+B;mBAAI,UAAU,OAAO,CAAC,KAAK;gBAAI;aAAO;QAC5E,SAAS,IAAM;mBAAI,UAAU,OAAO,CAAC,GAAG;gBAAE;aAAS;QACnD,QAAQ,CAAC,KAAe;mBAAI,UAAU,OAAO,CAAC,OAAO;gBAAI;aAAG;QAC5D,QAAQ,CAAC,QAAmB;mBAAI,UAAU,OAAO,CAAC,GAAG;gBAAE;gBAAU;aAAM;QACvE,UAAU,CAAC,QAAmB;mBAAI,UAAU,OAAO,CAAC,GAAG;gBAAE;gBAAY;aAAM;QAC3E,UAAU,CAAC,WAAqB;mBAAI,UAAU,OAAO,CAAC,GAAG;gBAAE;gBAAY;aAAS;IAClF;IACA,UAAU;QACR,KAAK;YAAC;SAAW;QACjB,QAAQ,CAAC,MAAgB;mBAAI,UAAU,QAAQ,CAAC,GAAG;gBAAE;aAAI;QACzD,UAAU,IAAM;mBAAI,UAAU,QAAQ,CAAC,GAAG;gBAAE;aAAW;QACvD,aAAa,IAAM;mBAAI,UAAU,QAAQ,CAAC,GAAG;gBAAE;aAAc;QAC7D,aAAa,IAAM;mBAAI,UAAU,QAAQ,CAAC,GAAG;gBAAE;aAAc;QAC7D,aAAa,IAAM;mBAAI,UAAU,QAAQ,CAAC,GAAG;gBAAE;aAAc;QAC7D,cAAc,IAAM;mBAAI,UAAU,QAAQ,CAAC,GAAG;gBAAE;aAAe;IACjE;AACF;AAGO,MAAM,WAAW;QAAC,0EAA0B,CAAC;;IAClD,OAAO,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU,UAAU,KAAK,CAAC,IAAI,CAAC;QAC/B,OAAO;iCAAE,IAAM,yHAAA,CAAA,SAAM,CAAC,KAAK,CAAC,QAAQ,CAAC;;QACrC,WAAW,IAAI,KAAK;IACtB;AACF;GANa;;QACJ,8KAAA,CAAA,WAAQ;;;AAOV,MAAM,UAAU,CAAC;;IACtB,OAAO,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU,UAAU,KAAK,CAAC,MAAM,CAAC;QACjC,OAAO;gCAAE,IAAM,yHAAA,CAAA,SAAM,CAAC,KAAK,CAAC,OAAO,CAAC;;QACpC,SAAS,CAAC,CAAC;IACb;AACF;IANa;;QACJ,8KAAA,CAAA,WAAQ;;;AAOV,MAAM,gBAAgB,CAAC;;IAC5B,OAAO,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU,UAAU,KAAK,CAAC,IAAI,CAAC;QAC/B,OAAO;sCAAE,IAAM,yHAAA,CAAA,SAAM,CAAC,KAAK,CAAC,aAAa,CAAC;;QAC1C,SAAS,CAAC,CAAC;IACb;AACF;IANa;;QACJ,8KAAA,CAAA,WAAQ;;;AAOV,MAAM,mBAAmB;QAAC,yEAAQ;;IACvC,OAAO,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU,UAAU,KAAK,CAAC,QAAQ,CAAC;QACnC,OAAO;yCAAE,IAAM,yHAAA,CAAA,SAAM,CAAC,KAAK,CAAC,gBAAgB,CAAC;;QAC7C,WAAW,KAAK,KAAK;IACvB;AACF;IANa;;QACJ,8KAAA,CAAA,WAAQ;;;AAOV,MAAM,iBAAiB;QAAC,yEAAQ;;IACrC,OAAO,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU,UAAU,KAAK,CAAC,MAAM,CAAC;QACjC,OAAO;uCAAE,IAAM,yHAAA,CAAA,SAAM,CAAC,KAAK,CAAC,cAAc,CAAC;;QAC3C,WAAW,IAAI,KAAK;IACtB;AACF;IANa;;QACJ,8KAAA,CAAA,WAAQ;;;AAQV,MAAM,cAAc;QAAC,0EAA6B,CAAC;;IACxD,OAAO,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU,UAAU,QAAQ,CAAC,IAAI,CAAC;QAClC,OAAO;oCAAE,IAAM,yHAAA,CAAA,SAAM,CAAC,QAAQ,CAAC,WAAW,CAAC;;QAC3C,WAAW,KAAK,KAAK;IACvB;AACF;IANa;;QACJ,8KAAA,CAAA,WAAQ;;;AAOV,MAAM,aAAa,CAAC;;IACzB,OAAO,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU,UAAU,QAAQ,CAAC,MAAM,CAAC;QACpC,OAAO;mCAAE,IAAM,yHAAA,CAAA,SAAM,CAAC,QAAQ,CAAC,UAAU,CAAC;;QAC1C,SAAS,CAAC,CAAC;IACb;AACF;IANa;;QACJ,8KAAA,CAAA,WAAQ;;;AAOV,MAAM,mBAAmB,CAAC;;IAC/B,OAAO,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU,UAAU,QAAQ,CAAC,IAAI,CAAC;QAClC,OAAO;yCAAE,IAAM,yHAAA,CAAA,SAAM,CAAC,QAAQ,CAAC,gBAAgB,CAAC;;QAChD,SAAS,CAAC,CAAC;IACb;AACF;IANa;;QACJ,8KAAA,CAAA,WAAQ;;;AAOV,MAAM,oBAAoB;;IAC/B,OAAO,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU,UAAU,QAAQ,CAAC,MAAM;QACnC,OAAO;0CAAE,IAAM,yHAAA,CAAA,SAAM,CAAC,QAAQ,CAAC,iBAAiB;;QAChD,WAAW,KAAK,KAAK;IACvB;AACF;IANa;;QACJ,8KAAA,CAAA,WAAQ;;;AAOV,MAAM,qBAAqB;QAAC,yEAAQ;;IACzC,OAAO,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU,UAAU,QAAQ,CAAC,OAAO,CAAC;QACrC,OAAO;2CAAE,IAAM,yHAAA,CAAA,SAAM,CAAC,QAAQ,CAAC,kBAAkB,CAAC;;QAClD,WAAW,KAAK,KAAK;IACvB;AACF;IANa;;QACJ,8KAAA,CAAA,WAAQ;;;AAOV,MAAM,wBAAwB,CAAC;;IACpC,OAAO,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU,UAAU,QAAQ,CAAC,QAAQ,CAAC;QACtC,OAAO;8CAAE,IAAM,yHAAA,CAAA,SAAM,CAAC,QAAQ,CAAC,qBAAqB,CAAC;;QACrD,SAAS,CAAC,CAAC;QACX,WAAW,KAAK,KAAK;IACvB;AACF;KAPa;;QACJ,8KAAA,CAAA,WAAQ;;;AASV,MAAM,cAAc;QAAC,0EAA6B,CAAC;;IACxD,OAAO,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU,UAAU,QAAQ,CAAC,IAAI,CAAC;QAClC,OAAO;oCAAE,IAAM,yHAAA,CAAA,SAAM,CAAC,QAAQ,CAAC,WAAW,CAAC;;QAC3C,WAAW,KAAK,KAAK;IACvB;AACF;KANa;;QACJ,8KAAA,CAAA,WAAQ;;;AAOV,MAAM,aAAa,CAAC;;IACzB,OAAO,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU,UAAU,QAAQ,CAAC,MAAM,CAAC;QACpC,OAAO;mCAAE,IAAM,yHAAA,CAAA,SAAM,CAAC,QAAQ,CAAC,UAAU,CAAC;;QAC1C,SAAS,CAAC,CAAC;IACb;AACF;KANa;;QACJ,8KAAA,CAAA,WAAQ;;;AAOV,MAAM,mBAAmB,CAAC;;IAC/B,OAAO,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU,UAAU,QAAQ,CAAC,IAAI,CAAC;QAClC,OAAO;yCAAE,IAAM,yHAAA,CAAA,SAAM,CAAC,QAAQ,CAAC,gBAAgB,CAAC;;QAChD,SAAS,CAAC,CAAC;IACb;AACF;KANa;;QACJ,8KAAA,CAAA,WAAQ;;;AAOV,MAAM,oBAAoB;;IAC/B,OAAO,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU,UAAU,QAAQ,CAAC,MAAM;QACnC,OAAO;0CAAE,IAAM,yHAAA,CAAA,SAAM,CAAC,QAAQ,CAAC,iBAAiB;;QAChD,WAAW,KAAK,KAAK;IACvB;AACF;KANa;;QACJ,8KAAA,CAAA,WAAQ;;;AAOV,MAAM,qBAAqB;QAAC,yEAAQ;;IACzC,OAAO,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU,UAAU,QAAQ,CAAC,OAAO,CAAC;QACrC,OAAO;2CAAE,IAAM,yHAAA,CAAA,SAAM,CAAC,QAAQ,CAAC,kBAAkB,CAAC;;QAClD,WAAW,KAAK,KAAK;IACvB;AACF;KANa;;QACJ,8KAAA,CAAA,WAAQ;;;AAOV,MAAM,sBAAsB;;IACjC,OAAO,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU,UAAU,QAAQ,CAAC,QAAQ;QACrC,OAAO;4CAAE,IAAM,yHAAA,CAAA,SAAM,CAAC,QAAQ,CAAC,mBAAmB;;QAClD,WAAW,KAAK,KAAK;IACvB;AACF;KANa;;QACJ,8KAAA,CAAA,WAAQ;;;AAQV,MAAM,kBAAkB;QAAC,0EAAiC,CAAC;;IAChE,OAAO,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU,UAAU,YAAY,CAAC,IAAI,CAAC;QACtC,OAAO;wCAAE,IAAM,yHAAA,CAAA,SAAM,CAAC,YAAY,CAAC,eAAe,CAAC;;QACnD,WAAW,IAAI,KAAK;IACtB;AACF;KANa;;QACJ,8KAAA,CAAA,WAAQ;;;AAOV,MAAM,iBAAiB,CAAC;;IAC7B,OAAO,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU,UAAU,YAAY,CAAC,MAAM,CAAC;QACxC,OAAO;uCAAE,IAAM,yHAAA,CAAA,SAAM,CAAC,YAAY,CAAC,cAAc,CAAC;;QAClD,SAAS,CAAC,CAAC;IACb;AACF;KANa;;QACJ,8KAAA,CAAA,WAAQ;;;AAOV,MAAM,0BAA0B,CAAC;;IACtC,OAAO,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU,UAAU,YAAY,CAAC,QAAQ,CAAC;QAC1C,OAAO;gDAAE,IAAM,yHAAA,CAAA,SAAM,CAAC,YAAY,CAAC,uBAAuB,CAAC;;QAC3D,WAAW,KAAK,KAAK;IACvB;AACF;KANa;;QACJ,8KAAA,CAAA,WAAQ;;;AAOV,MAAM,2BAA2B,CAAC;;IACvC,OAAO,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU,UAAU,YAAY,CAAC,OAAO,CAAC;QACzC,OAAO;iDAAE,IAAM,yHAAA,CAAA,SAAM,CAAC,YAAY,CAAC,wBAAwB,CAAC;;QAC5D,SAAS,CAAC,CAAC;QACX,WAAW,KAAK,KAAK;IACvB;AACF;KAPa;;QACJ,8KAAA,CAAA,WAAQ;;;AASV,MAAM,uBAAuB;;IAClC,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QACjB,UAAU;gDAAE,CAAC,OACX,yHAAA,CAAA,SAAM,CAAC,YAAY,CAAC,iBAAiB,CAAC;;QACxC,SAAS;gDAAE;gBACT,kCAAkC;gBAClC,YAAY,iBAAiB,CAAC;oBAAE,UAAU,UAAU,YAAY,CAAC,GAAG;gBAAC;YACvE;;IACF;AACF;KAXa;;QACS,yLAAA,CAAA,iBAAc;QAE3B,iLAAA,CAAA,cAAW;;;AAWb,MAAM,aAAa;QAAC,0EAA6B,CAAC;;IACvD,OAAO,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU,UAAU,OAAO,CAAC,IAAI,CAAC;QACjC,OAAO;mCAAE,IAAM,yHAAA,CAAA,SAAM,CAAC,OAAO,CAAC,UAAU,CAAC;;QACzC,WAAW,KAAK,KAAK;IACvB;AACF;KANa;;QACJ,8KAAA,CAAA,WAAQ;;;AAOV,MAAM,iBAAiB,CAAC;;IAC7B,OAAO,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU,UAAU,OAAO,CAAC,MAAM,CAAC;QACnC,OAAO;uCAAE,IAAM,yHAAA,CAAA,SAAM,CAAC,OAAO,CAAC,cAAc,CAAC;;QAC7C,SAAS,CAAC,CAAC;IACb;AACF;KANa;;QACJ,8KAAA,CAAA,WAAQ;;;AAOV,MAAM,mBAAmB,CAAC;;IAC/B,OAAO,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU,UAAU,OAAO,CAAC,MAAM,CAAC;QACnC,OAAO;yCAAE,IAAM,yHAAA,CAAA,SAAM,CAAC,OAAO,CAAC,gBAAgB,CAAC;;QAC/C,WAAW,KAAK,KAAK;IACvB;AACF;KANa;;QACJ,8KAAA,CAAA,WAAQ;;;AAOV,MAAM,qBAAqB;QAAC,yEAAQ;;IACzC,OAAO,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU,UAAU,OAAO,CAAC,QAAQ,CAAC;QACrC,OAAO;2CAAE,IAAM,yHAAA,CAAA,SAAM,CAAC,OAAO,CAAC,kBAAkB,CAAC;;QACjD,WAAW,KAAK,KAAK;IACvB;AACF;KANa;;QACJ,8KAAA,CAAA,WAAQ;;;AAOV,MAAM,uBAAuB,CAAC;;IACnC,OAAO,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU,UAAU,OAAO,CAAC,QAAQ,CAAC;QACrC,OAAO;6CAAE,IAAM,yHAAA,CAAA,SAAM,CAAC,OAAO,CAAC,oBAAoB,CAAC;;QACnD,SAAS,CAAC,CAAC;QACX,WAAW,KAAK,KAAK;IACvB;AACF;KAPa;;QACJ,8KAAA,CAAA,WAAQ;;;AASV,MAAM,cAAc;;IACzB,OAAO,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU,UAAU,QAAQ,CAAC,GAAG;QAChC,OAAO;oCAAE,IAAM,yHAAA,CAAA,SAAM,CAAC,QAAQ,CAAC,WAAW;;QAC1C,WAAW,KAAK,KAAK;IACvB;AACF;KANa;;QACJ,8KAAA,CAAA,WAAQ;;;AAOV,MAAM,aAAa,CAAC;;IACzB,OAAO,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU,UAAU,QAAQ,CAAC,MAAM,CAAC;QACpC,OAAO;mCAAE,IAAM,yHAAA,CAAA,SAAM,CAAC,QAAQ,CAAC,UAAU,CAAC;;QAC1C,SAAS,CAAC,CAAC;QACX,WAAW,KAAK,KAAK;IACvB;AACF;KAPa;;QACJ,8KAAA,CAAA,WAAQ;;;AAQV,MAAM,cAAc;;IACzB,OAAO,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU,UAAU,QAAQ,CAAC,QAAQ;QACrC,OAAO;oCAAE,IAAM,yHAAA,CAAA,SAAM,CAAC,QAAQ,CAAC,WAAW;;QAC1C,WAAW,KAAK,KAAK;IACvB;AACF;KANa;;QACJ,8KAAA,CAAA,WAAQ;;;AAOV,MAAM,iBAAiB;;IAC5B,OAAO,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU,UAAU,QAAQ,CAAC,WAAW;QACxC,OAAO;uCAAE,IAAM,yHAAA,CAAA,SAAM,CAAC,QAAQ,CAAC,cAAc;;QAC7C,WAAW,KAAK,KAAK;IACvB;AACF;KANa;;QACJ,8KAAA,CAAA,WAAQ;;;AAOV,MAAM,iBAAiB;;IAC5B,OAAO,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU,UAAU,QAAQ,CAAC,WAAW;QACxC,OAAO;uCAAE,IAAM,yHAAA,CAAA,SAAM,CAAC,QAAQ,CAAC,cAAc;;QAC7C,WAAW,KAAK,KAAK;IACvB;AACF;KANa;;QACJ,8KAAA,CAAA,WAAQ;;;AAOV,MAAM,iBAAiB;;IAC5B,OAAO,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU,UAAU,QAAQ,CAAC,WAAW;QACxC,OAAO;uCAAE,IAAM,yHAAA,CAAA,SAAM,CAAC,QAAQ,CAAC,cAAc;;QAC7C,WAAW,KAAK,KAAK;IACvB;AACF;KANa;;QACJ,8KAAA,CAAA,WAAQ;;;AAOV,MAAM,kBAAkB;;IAC7B,OAAO,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU,UAAU,QAAQ,CAAC,YAAY;QACzC,OAAO;wCAAE,IAAM,yHAAA,CAAA,SAAM,CAAC,QAAQ,CAAC,eAAe;;QAC9C,WAAW,KAAK,KAAK;IACvB;AACF;KANa;;QACJ,8KAAA,CAAA,WAAQ", "debugId": null}}, {"offset": {"line": 2402, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/anjali-portfolio/anjali/src/components/sections/packages-preview.tsx"], "sourcesContent": ["'use client'\n\nimport Link from 'next/link'\nimport Image from 'next/image'\nimport { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>rk<PERSON>, Loader2, AlertCircle } from 'lucide-react'\nimport { But<PERSON> } from '@/components/ui/button'\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Badge } from '@/components/ui/badge'\nimport { Section, SectionHeader } from '@/components/ui/section'\nimport { AnimatedElement, StaggeredContainer, StaggeredItem } from '@/components/ui/animated-element'\nimport { usePopularPackages, useContactInfo } from '@/hooks/use-api'\nimport { generateWhatsAppLink } from '@/lib/utils'\n\nexport default function PackagesPreview() {\n  const { data: packagesData, isLoading, error } = usePopularPackages(2)\n  const { data: contactInfo } = useContactInfo()\n\n  const packages = packagesData?.packages || []\n\n  // Loading state\n  if (isLoading) {\n    return (\n      <Section id=\"packages\">\n        <AnimatedElement animation=\"fadeIn\">\n          <SectionHeader\n            subtitle=\"Special Packages\"\n            title=\"Save More with Our Packages\"\n            description=\"Curated combinations of our most popular services at special prices. Perfect for brides and special occasions.\"\n          />\n        </AnimatedElement>\n        <div className=\"flex justify-center items-center py-20\">\n          <Loader2 className=\"w-8 h-8 animate-spin text-rose-gold\" />\n          <span className=\"ml-3 text-text-secondary\">Loading packages...</span>\n        </div>\n      </Section>\n    )\n  }\n\n  // Error state\n  if (error) {\n    return (\n      <Section id=\"packages\">\n        <AnimatedElement animation=\"fadeIn\">\n          <SectionHeader\n            subtitle=\"Special Packages\"\n            title=\"Save More with Our Packages\"\n            description=\"Curated combinations of our most popular services at special prices. Perfect for brides and special occasions.\"\n          />\n        </AnimatedElement>\n        <div className=\"text-center py-20\">\n          <div className=\"w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4\">\n            <AlertCircle className=\"w-8 h-8 text-red-600\" />\n          </div>\n          <h3 className=\"font-display text-xl font-semibold text-text-primary mb-2\">\n            Unable to Load Packages\n          </h3>\n          <p className=\"text-text-secondary mb-6\">\n            We're having trouble loading our packages. Please try again later.\n          </p>\n          <Button variant=\"outline\" onClick={() => window.location.reload()}>\n            Try Again\n          </Button>\n        </div>\n      </Section>\n    )\n  }\n\n  // No packages state\n  if (packages.length === 0) {\n    return null\n  }\n\n  return (\n    <Section id=\"packages\">\n      <AnimatedElement animation=\"fadeIn\">\n        <SectionHeader\n          subtitle=\"Special Packages\"\n          title=\"Complete Beauty Packages\"\n          description=\"Save more with our carefully curated packages that combine multiple services for your special occasions.\"\n        />\n      </AnimatedElement>\n\n      <StaggeredContainer className=\"grid md:grid-cols-2 gap-8 mb-12\">\n        {packages.map((pkg, index) => (\n          <StaggeredItem key={pkg.id}>\n            <Card className=\"group relative overflow-hidden hover:shadow-2xl transition-all duration-300 border-0 bg-white\">\n              {/* Background Gradient */}\n              <div className=\"absolute inset-0 bg-gradient-to-br from-rose-gold/5 to-blush-pink/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300\"></div>\n              \n              {/* Badge */}\n              {pkg.popular && (\n                <Badge\n                  className=\"absolute top-4 right-4 z-10\"\n                  variant=\"default\"\n                >\n                  <Sparkles className=\"w-3 h-3 mr-1\" />\n                  Popular\n                </Badge>\n              )}\n\n              <div className=\"relative aspect-[16/9] overflow-hidden\">\n                <Image\n                  src={pkg.image || `https://images.unsplash.com/photo-1596462502278-27bfdc403348?w=600&h=400&fit=crop&crop=face&q=80`}\n                  alt={pkg.name}\n                  fill\n                  className=\"object-cover group-hover:scale-105 transition-transform duration-300\"\n                />\n              </div>\n\n              <CardHeader className=\"relative\">\n                <CardTitle className=\"text-2xl group-hover:text-rose-gold-dark transition-colors\">\n                  {pkg.name}\n                </CardTitle>\n                <CardDescription className=\"text-text-secondary\">\n                  {pkg.description}\n                </CardDescription>\n              </CardHeader>\n              \n              <CardContent className=\"relative space-y-6\">\n                {/* Pricing */}\n                <div className=\"flex items-center gap-3\">\n                  <div className=\"text-3xl font-bold text-text-primary\">\n                    {pkg.price}\n                  </div>\n                  {pkg.originalPrice && (\n                    <>\n                      <div className=\"text-lg text-text-muted line-through\">\n                        {pkg.originalPrice}\n                      </div>\n                      <Badge variant=\"success\" className=\"text-xs\">\n                        Save Money\n                      </Badge>\n                    </>\n                  )}\n                </div>\n\n                {/* Duration */}\n                <div className=\"flex items-center gap-2 text-text-secondary\">\n                  <Clock className=\"w-4 h-4\" />\n                  <span className=\"text-sm\">{pkg.duration}</span>\n                </div>\n                \n                {/* Services Included */}\n                <div className=\"space-y-3\">\n                  <h4 className=\"font-semibold text-text-primary\">Includes:</h4>\n                  <ul className=\"space-y-2\">\n                    {pkg.services.map((service, idx) => (\n                      <li key={idx} className=\"text-sm text-text-secondary flex items-start gap-3\">\n                        <Check className=\"w-4 h-4 text-rose-gold-dark mt-0.5 flex-shrink-0\" />\n                        {service}\n                      </li>\n                    ))}\n                  </ul>\n                </div>\n\n                {/* Features List */}\n                {pkg.features.length > 0 && (\n                  <div className=\"space-y-3\">\n                    <h4 className=\"font-semibold text-text-primary\">Additional Features:</h4>\n                    <ul className=\"space-y-2\">\n                      {pkg.features.map((feature, idx) => (\n                        <li key={idx} className=\"text-sm text-text-secondary flex items-start gap-2\">\n                          <Check className=\"w-4 h-4 text-rose-gold-dark mt-0.5 flex-shrink-0\" />\n                          <span>{feature}</span>\n                        </li>\n                      ))}\n                    </ul>\n                  </div>\n                )}\n                \n                {/* CTA Button */}\n                <Button \n                  asChild \n                  variant=\"gradient\" \n                  className=\"w-full group\"\n                  size=\"lg\"\n                >\n                  <Link\n                    href={generateWhatsAppLink(\n                      contactInfo?.phone || '',\n                      `Hi! I'm interested in the ${pkg.name} package. Could you provide more details?`\n                    )}\n                    target=\"_blank\"\n                    rel=\"noopener noreferrer\"\n                  >\n                    Book This Package\n                    <ArrowRight className=\"w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform\" />\n                  </Link>\n                </Button>\n              </CardContent>\n            </Card>\n          </StaggeredItem>\n        ))}\n      </StaggeredContainer>\n\n      <AnimatedElement animation=\"slideUp\" delay={0.6} className=\"text-center\">\n        <Button asChild variant=\"outline\" size=\"lg\">\n          <Link href=\"/packages\">\n            View All Packages\n            <ArrowRight className=\"w-5 h-5 ml-2\" />\n          </Link>\n        </Button>\n      </AnimatedElement>\n    </Section>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAXA;;;;;;;;;;;AAae,SAAS;;IACtB,MAAM,EAAE,MAAM,YAAY,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,6HAAA,CAAA,qBAAkB,AAAD,EAAE;IACpE,MAAM,EAAE,MAAM,WAAW,EAAE,GAAG,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD;IAE3C,MAAM,WAAW,CAAA,yBAAA,mCAAA,aAAc,QAAQ,KAAI,EAAE;IAE7C,gBAAgB;IAChB,IAAI,WAAW;QACb,qBACE,6LAAC,sIAAA,CAAA,UAAO;YAAC,IAAG;;8BACV,6LAAC,kJAAA,CAAA,kBAAe;oBAAC,WAAU;8BACzB,cAAA,6LAAC,sIAAA,CAAA,gBAAa;wBACZ,UAAS;wBACT,OAAM;wBACN,aAAY;;;;;;;;;;;8BAGhB,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,oNAAA,CAAA,UAAO;4BAAC,WAAU;;;;;;sCACnB,6LAAC;4BAAK,WAAU;sCAA2B;;;;;;;;;;;;;;;;;;IAInD;IAEA,cAAc;IACd,IAAI,OAAO;QACT,qBACE,6LAAC,sIAAA,CAAA,UAAO;YAAC,IAAG;;8BACV,6LAAC,kJAAA,CAAA,kBAAe;oBAAC,WAAU;8BACzB,cAAA,6LAAC,sIAAA,CAAA,gBAAa;wBACZ,UAAS;wBACT,OAAM;wBACN,aAAY;;;;;;;;;;;8BAGhB,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,uNAAA,CAAA,cAAW;gCAAC,WAAU;;;;;;;;;;;sCAEzB,6LAAC;4BAAG,WAAU;sCAA4D;;;;;;sCAG1E,6LAAC;4BAAE,WAAU;sCAA2B;;;;;;sCAGxC,6LAAC,qIAAA,CAAA,SAAM;4BAAC,SAAQ;4BAAU,SAAS,IAAM,OAAO,QAAQ,CAAC,MAAM;sCAAI;;;;;;;;;;;;;;;;;;IAM3E;IAEA,oBAAoB;IACpB,IAAI,SAAS,MAAM,KAAK,GAAG;QACzB,OAAO;IACT;IAEA,qBACE,6LAAC,sIAAA,CAAA,UAAO;QAAC,IAAG;;0BACV,6LAAC,kJAAA,CAAA,kBAAe;gBAAC,WAAU;0BACzB,cAAA,6LAAC,sIAAA,CAAA,gBAAa;oBACZ,UAAS;oBACT,OAAM;oBACN,aAAY;;;;;;;;;;;0BAIhB,6LAAC,kJAAA,CAAA,qBAAkB;gBAAC,WAAU;0BAC3B,SAAS,GAAG,CAAC,CAAC,KAAK,sBAClB,6LAAC,kJAAA,CAAA,gBAAa;kCACZ,cAAA,6LAAC,mIAAA,CAAA,OAAI;4BAAC,WAAU;;8CAEd,6LAAC;oCAAI,WAAU;;;;;;gCAGd,IAAI,OAAO,kBACV,6LAAC,oIAAA,CAAA,QAAK;oCACJ,WAAU;oCACV,SAAQ;;sDAER,6LAAC,6MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;8CAKzC,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,gIAAA,CAAA,UAAK;wCACJ,KAAK,IAAI,KAAK,IAAK;wCACnB,KAAK,IAAI,IAAI;wCACb,IAAI;wCACJ,WAAU;;;;;;;;;;;8CAId,6LAAC,mIAAA,CAAA,aAAU;oCAAC,WAAU;;sDACpB,6LAAC,mIAAA,CAAA,YAAS;4CAAC,WAAU;sDAClB,IAAI,IAAI;;;;;;sDAEX,6LAAC,mIAAA,CAAA,kBAAe;4CAAC,WAAU;sDACxB,IAAI,WAAW;;;;;;;;;;;;8CAIpB,6LAAC,mIAAA,CAAA,cAAW;oCAAC,WAAU;;sDAErB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DACZ,IAAI,KAAK;;;;;;gDAEX,IAAI,aAAa,kBAChB;;sEACE,6LAAC;4DAAI,WAAU;sEACZ,IAAI,aAAa;;;;;;sEAEpB,6LAAC,oIAAA,CAAA,QAAK;4DAAC,SAAQ;4DAAU,WAAU;sEAAU;;;;;;;;;;;;;;sDAQnD,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,uMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;8DACjB,6LAAC;oDAAK,WAAU;8DAAW,IAAI,QAAQ;;;;;;;;;;;;sDAIzC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAG,WAAU;8DAAkC;;;;;;8DAChD,6LAAC;oDAAG,WAAU;8DACX,IAAI,QAAQ,CAAC,GAAG,CAAC,CAAC,SAAS,oBAC1B,6LAAC;4DAAa,WAAU;;8EACtB,6LAAC,uMAAA,CAAA,QAAK;oEAAC,WAAU;;;;;;gEAChB;;2DAFM;;;;;;;;;;;;;;;;wCASd,IAAI,QAAQ,CAAC,MAAM,GAAG,mBACrB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAG,WAAU;8DAAkC;;;;;;8DAChD,6LAAC;oDAAG,WAAU;8DACX,IAAI,QAAQ,CAAC,GAAG,CAAC,CAAC,SAAS,oBAC1B,6LAAC;4DAAa,WAAU;;8EACtB,6LAAC,uMAAA,CAAA,QAAK;oEAAC,WAAU;;;;;;8EACjB,6LAAC;8EAAM;;;;;;;2DAFA;;;;;;;;;;;;;;;;sDAUjB,6LAAC,qIAAA,CAAA,SAAM;4CACL,OAAO;4CACP,SAAQ;4CACR,WAAU;4CACV,MAAK;sDAEL,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDACH,MAAM,CAAA,GAAA,sHAAA,CAAA,uBAAoB,AAAD,EACvB,CAAA,wBAAA,kCAAA,YAAa,KAAK,KAAI,IACtB,AAAC,6BAAqC,OAAT,IAAI,IAAI,EAAC;gDAExC,QAAO;gDACP,KAAI;;oDACL;kEAEC,6LAAC,qNAAA,CAAA,aAAU;wDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;uBAtGZ,IAAI,EAAE;;;;;;;;;;0BA+G9B,6LAAC,kJAAA,CAAA,kBAAe;gBAAC,WAAU;gBAAU,OAAO;gBAAK,WAAU;0BACzD,cAAA,6LAAC,qIAAA,CAAA,SAAM;oBAAC,OAAO;oBAAC,SAAQ;oBAAU,MAAK;8BACrC,cAAA,6LAAC,+JAAA,CAAA,UAAI;wBAAC,MAAK;;4BAAY;0CAErB,6LAAC,qNAAA,CAAA,aAAU;gCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMlC;GAhMwB;;QAC2B,6HAAA,CAAA,qBAAkB;QACrC,6HAAA,CAAA,iBAAc;;;KAFtB", "debugId": null}}, {"offset": {"line": 2927, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/anjali-portfolio/anjali/src/components/sections/testimonials.tsx"], "sourcesContent": ["'use client'\n\nimport Image from 'next/image'\nimport { <PERSON>, Quote, ChevronLeft, ChevronRight } from 'lucide-react'\nimport { Card, CardContent } from '@/components/ui/card'\nimport { Section, SectionHeader } from '@/components/ui/section'\nimport { AnimatedElement, StaggeredContainer, StaggeredItem } from '@/components/ui/animated-element'\nimport { useApprovedTestimonials } from '@/hooks/use-api'\nimport { formatDate } from '@/lib/utils'\nimport { But<PERSON> } from '@/components/ui/button'\nimport { useState } from 'react'\n\n// Fallback testimonials when API is not available\nconst fallbackTestimonials = [\n  {\n    id: 'fallback-1',\n    name: '<PERSON>',\n    email: '<EMAIL>',\n    message: '<PERSON> brings military precision and security expertise to our high-profile transportation services. His vigilance and professionalism ensure the safety of our most important clients.',\n    rating: 5,\n    service: 'Security Specialist',\n    specialties: ['Executive Security', 'Risk Assessment', 'Protocol Management'],\n    image: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=400&h=400&fit=crop&crop=face&q=80',\n    status: 'APPROVED' as const,\n    createdAt: new Date().toISOString(),\n    updatedAt: new Date().toISOString(),\n  },\n  {\n    id: 'fallback-2',\n    name: 'Sarah <PERSON>',\n    message: 'Sarah has transformed our client experience with her attention to detail and exceptional service standards. Her dedication to excellence is unmatched.',\n    rating: 5,\n    service: 'Client Relations Manager',\n    specialties: ['Customer Service', 'Team Leadership', 'Process Optimization'],\n    image: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=400&h=400&fit=crop&crop=face&q=80',\n    status: 'APPROVED' as const,\n    createdAt: new Date().toISOString(),\n    updatedAt: new Date().toISOString(),\n  },\n  {\n    id: 'fallback-3',\n    name: 'Michael Chen',\n    email: '<EMAIL>',\n    message: 'Michael\\'s technical expertise and innovative solutions have revolutionized our operations. His ability to solve complex problems is truly remarkable.',\n    rating: 5,\n    service: 'Technical Director',\n    specialties: ['System Architecture', 'Innovation', 'Problem Solving'],\n    image: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=400&h=400&fit=crop&crop=face&q=80',\n    status: 'APPROVED' as const,\n    createdAt: new Date().toISOString(),\n    updatedAt: new Date().toISOString(),\n  },\n]\n\nexport default function Testimonials() {\n  const { data: testimonialsData } = useApprovedTestimonials(6)\n  const [currentIndex, setCurrentIndex] = useState(0)\n\n  // Use API data if available, otherwise use fallback testimonials\n  const testimonials = (testimonialsData?.testimonials && testimonialsData.testimonials.length > 0)\n    ? testimonialsData.testimonials\n    : fallbackTestimonials\n\n  const isUsingFallback = !testimonialsData?.testimonials || testimonialsData.testimonials.length === 0\n\n  const nextTestimonial = () => {\n    setCurrentIndex((prev) => (prev + 1) % testimonials.length)\n  }\n\n  const prevTestimonial = () => {\n    setCurrentIndex((prev) => (prev - 1 + testimonials.length) % testimonials.length)\n  }\n\n  const currentTestimonial = testimonials[currentIndex]\n\n  return (\n    <Section background=\"gradient\" id=\"testimonials\">\n      <AnimatedElement animation=\"fadeIn\">\n        <SectionHeader\n          subtitle=\"Client Reviews\"\n          title=\"What Our Clients Say\"\n          description={isUsingFallback\n            ? \"Read what our satisfied clients have to say about their experience with our professional makeup services. (Sample reviews - connect to CMS for live testimonials)\"\n            : \"Read what our satisfied clients have to say about their experience with our professional makeup services.\"\n          }\n        />\n      </AnimatedElement>\n\n      {/* Main Testimonial Display */}\n      <AnimatedElement animation=\"slideUp\" delay={0.2} className=\"mt-16\">\n        <div className=\"relative max-w-6xl mx-auto\">\n          <div className=\"grid lg:grid-cols-2 gap-8 lg:gap-12 items-center\">\n            {/* Large Profile Image */}\n            <div className=\"relative order-2 lg:order-1\">\n              <div className=\"relative w-full aspect-[3/4] lg:aspect-[4/5] max-w-sm mx-auto lg:max-w-none rounded-2xl overflow-hidden bg-gradient-to-br from-rose-gold to-blush-pink\">\n                <Image\n                  src={currentTestimonial.image || `https://images.unsplash.com/photo-1494790108755-2616b612b786?w=600&h=750&fit=crop&crop=face&q=80`}\n                  alt={currentTestimonial.name}\n                  fill\n                  className=\"object-cover\"\n                />\n              </div>\n            </div>\n\n            {/* Testimonial Content */}\n            <div className=\"space-y-6 lg:space-y-8 order-1 lg:order-2 text-center lg:text-left\">\n              {/* Large Quote Mark */}\n              <div className=\"text-4xl lg:text-6xl text-rose-gold/30 font-serif leading-none\">\n                \"\n              </div>\n\n              {/* Client Name and Service */}\n              <div className=\"space-y-2\">\n                <h3 className=\"text-xl lg:text-2xl font-bold text-text-primary\">\n                  {currentTestimonial.name}\n                </h3>\n                {currentTestimonial.service && (\n                  <p className=\"text-blue-600 font-medium\">\n                    {currentTestimonial.service}\n                  </p>\n                )}\n              </div>\n\n              {/* Testimonial Text */}\n              <blockquote className=\"text-base lg:text-lg text-text-secondary leading-relaxed\">\n                {currentTestimonial.message}\n              </blockquote>\n\n              {/* Navigation Dots */}\n              <div className=\"flex items-center justify-center lg:justify-start gap-2\">\n                {testimonials.map((_, index) => (\n                  <button\n                    key={index}\n                    onClick={() => setCurrentIndex(index)}\n                    className={`w-3 h-3 rounded-full transition-colors ${\n                      index === currentIndex\n                        ? 'bg-blue-600'\n                        : 'bg-gray-300 hover:bg-gray-400'\n                    }`}\n                  />\n                ))}\n              </div>\n\n              {/* Mobile Navigation Buttons */}\n              <div className=\"flex items-center justify-center gap-4 lg:hidden\">\n                <button\n                  onClick={prevTestimonial}\n                  className=\"w-10 h-10 bg-white/80 backdrop-blur-sm rounded-full shadow-lg hover:shadow-xl transition-all duration-300 flex items-center justify-center text-text-primary hover:bg-white\"\n                >\n                  <ChevronLeft className=\"w-5 h-5\" />\n                </button>\n\n                <button\n                  onClick={nextTestimonial}\n                  className=\"w-10 h-10 bg-white/80 backdrop-blur-sm rounded-full shadow-lg hover:shadow-xl transition-all duration-300 flex items-center justify-center text-text-primary hover:bg-white\"\n                >\n                  <ChevronRight className=\"w-5 h-5\" />\n                </button>\n              </div>\n            </div>\n          </div>\n\n          {/* Navigation Arrows */}\n          <button\n            onClick={prevTestimonial}\n            className=\"absolute -left-16 top-1/2 -translate-y-1/2 w-12 h-12 bg-white/80 backdrop-blur-sm rounded-full shadow-lg hover:shadow-xl transition-all duration-300 hidden lg:flex items-center justify-center text-text-primary hover:bg-white\"\n          >\n            <ChevronLeft className=\"w-6 h-6\" />\n          </button>\n\n          <button\n            onClick={nextTestimonial}\n            className=\"absolute -right-16 top-1/2 -translate-y-1/2 w-12 h-12 bg-white/80 backdrop-blur-sm rounded-full shadow-lg hover:shadow-xl transition-all duration-300 hidden lg:flex items-center justify-center text-text-primary hover:bg-white\"\n          >\n            <ChevronRight className=\"w-6 h-6\" />\n          </button>\n        </div>\n      </AnimatedElement>\n\n      {/* Stats Section */}\n      <AnimatedElement animation=\"slideUp\" delay={0.8} className=\"mt-24\">\n        <div className=\"grid grid-cols-2 md:grid-cols-4 gap-8 text-center\">\n          <div className=\"space-y-2\">\n            <div className=\"text-3xl md:text-4xl font-bold text-text-primary\">50+</div>\n            <div className=\"text-text-secondary\">Happy Clients</div>\n          </div>\n          <div className=\"space-y-2\">\n            <div className=\"text-3xl md:text-4xl font-bold text-text-primary\">5.0</div>\n            <div className=\"text-text-secondary\">Average Rating</div>\n          </div>\n          <div className=\"space-y-2\">\n            <div className=\"text-3xl md:text-4xl font-bold text-text-primary\">100+</div>\n            <div className=\"text-text-secondary\">Makeup Sessions</div>\n          </div>\n          <div className=\"space-y-2\">\n            <div className=\"text-3xl md:text-4xl font-bold text-text-primary\">7</div>\n            <div className=\"text-text-secondary\">Cities Served</div>\n          </div>\n        </div>\n      </AnimatedElement>\n    </Section>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAEA;AACA;AACA;AAGA;;;AAVA;;;;;;;AAYA,kDAAkD;AAClD,MAAM,uBAAuB;IAC3B;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,SAAS;QACT,QAAQ;QACR,SAAS;QACT,aAAa;YAAC;YAAsB;YAAmB;SAAsB;QAC7E,OAAO;QACP,QAAQ;QACR,WAAW,IAAI,OAAO,WAAW;QACjC,WAAW,IAAI,OAAO,WAAW;IACnC;IACA;QACE,IAAI;QACJ,MAAM;QACN,SAAS;QACT,QAAQ;QACR,SAAS;QACT,aAAa;YAAC;YAAoB;YAAmB;SAAuB;QAC5E,OAAO;QACP,QAAQ;QACR,WAAW,IAAI,OAAO,WAAW;QACjC,WAAW,IAAI,OAAO,WAAW;IACnC;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,SAAS;QACT,QAAQ;QACR,SAAS;QACT,aAAa;YAAC;YAAuB;YAAc;SAAkB;QACrE,OAAO;QACP,QAAQ;QACR,WAAW,IAAI,OAAO,WAAW;QACjC,WAAW,IAAI,OAAO,WAAW;IACnC;CACD;AAEc,SAAS;;IACtB,MAAM,EAAE,MAAM,gBAAgB,EAAE,GAAG,CAAA,GAAA,6HAAA,CAAA,0BAAuB,AAAD,EAAE;IAC3D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,iEAAiE;IACjE,MAAM,eAAe,AAAC,CAAA,6BAAA,uCAAA,iBAAkB,YAAY,KAAI,iBAAiB,YAAY,CAAC,MAAM,GAAG,IAC3F,iBAAiB,YAAY,GAC7B;IAEJ,MAAM,kBAAkB,EAAC,6BAAA,uCAAA,iBAAkB,YAAY,KAAI,iBAAiB,YAAY,CAAC,MAAM,KAAK;IAEpG,MAAM,kBAAkB;QACtB,gBAAgB,CAAC,OAAS,CAAC,OAAO,CAAC,IAAI,aAAa,MAAM;IAC5D;IAEA,MAAM,kBAAkB;QACtB,gBAAgB,CAAC,OAAS,CAAC,OAAO,IAAI,aAAa,MAAM,IAAI,aAAa,MAAM;IAClF;IAEA,MAAM,qBAAqB,YAAY,CAAC,aAAa;IAErD,qBACE,6LAAC,sIAAA,CAAA,UAAO;QAAC,YAAW;QAAW,IAAG;;0BAChC,6LAAC,kJAAA,CAAA,kBAAe;gBAAC,WAAU;0BACzB,cAAA,6LAAC,sIAAA,CAAA,gBAAa;oBACZ,UAAS;oBACT,OAAM;oBACN,aAAa,kBACT,sKACA;;;;;;;;;;;0BAMR,6LAAC,kJAAA,CAAA,kBAAe;gBAAC,WAAU;gBAAU,OAAO;gBAAK,WAAU;0BACzD,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,gIAAA,CAAA,UAAK;4CACJ,KAAK,mBAAmB,KAAK,IAAK;4CAClC,KAAK,mBAAmB,IAAI;4CAC5B,IAAI;4CACJ,WAAU;;;;;;;;;;;;;;;;8CAMhB,6LAAC;oCAAI,WAAU;;sDAEb,6LAAC;4CAAI,WAAU;sDAAiE;;;;;;sDAKhF,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAG,WAAU;8DACX,mBAAmB,IAAI;;;;;;gDAEzB,mBAAmB,OAAO,kBACzB,6LAAC;oDAAE,WAAU;8DACV,mBAAmB,OAAO;;;;;;;;;;;;sDAMjC,6LAAC;4CAAW,WAAU;sDACnB,mBAAmB,OAAO;;;;;;sDAI7B,6LAAC;4CAAI,WAAU;sDACZ,aAAa,GAAG,CAAC,CAAC,GAAG,sBACpB,6LAAC;oDAEC,SAAS,IAAM,gBAAgB;oDAC/B,WAAW,AAAC,0CAIX,OAHC,UAAU,eACN,gBACA;mDALD;;;;;;;;;;sDAYX,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDACC,SAAS;oDACT,WAAU;8DAEV,cAAA,6LAAC,uNAAA,CAAA,cAAW;wDAAC,WAAU;;;;;;;;;;;8DAGzB,6LAAC;oDACC,SAAS;oDACT,WAAU;8DAEV,cAAA,6LAAC,yNAAA,CAAA,eAAY;wDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAOhC,6LAAC;4BACC,SAAS;4BACT,WAAU;sCAEV,cAAA,6LAAC,uNAAA,CAAA,cAAW;gCAAC,WAAU;;;;;;;;;;;sCAGzB,6LAAC;4BACC,SAAS;4BACT,WAAU;sCAEV,cAAA,6LAAC,yNAAA,CAAA,eAAY;gCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;0BAM9B,6LAAC,kJAAA,CAAA,kBAAe;gBAAC,WAAU;gBAAU,OAAO;gBAAK,WAAU;0BACzD,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CAAmD;;;;;;8CAClE,6LAAC;oCAAI,WAAU;8CAAsB;;;;;;;;;;;;sCAEvC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CAAmD;;;;;;8CAClE,6LAAC;oCAAI,WAAU;8CAAsB;;;;;;;;;;;;sCAEvC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CAAmD;;;;;;8CAClE,6LAAC;oCAAI,WAAU;8CAAsB;;;;;;;;;;;;sCAEvC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CAAmD;;;;;;8CAClE,6LAAC;oCAAI,WAAU;8CAAsB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMjD;GApJwB;;QACa,6HAAA,CAAA,0BAAuB;;;KADpC", "debugId": null}}, {"offset": {"line": 3361, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/anjali-portfolio/anjali/src/components/sections/gallery-showcase.tsx"], "sourcesContent": ["'use client'\n\nimport Link from 'next/link'\nimport Image from 'next/image'\nimport { <PERSON><PERSON><PERSON>, Eye } from 'lucide-react'\nimport { <PERSON><PERSON> } from '@/components/ui/button'\nimport { Badge } from '@/components/ui/badge'\nimport { Section, SectionHeader } from '@/components/ui/section'\nimport { AnimatedElement, StaggeredContainer, StaggeredItem } from '@/components/ui/animated-element'\nimport { useFeaturedGallery } from '@/hooks/use-api'\n\n// Fallback gallery data when API is not available\nconst fallbackGallery = [\n  {\n    id: 'fallback-1',\n    title: 'Bridal Elegance',\n    description: 'Classic bridal makeup with timeless elegance',\n    image: 'https://images.unsplash.com/photo-1596462502278-27bfdc403348?w=400&h=400&fit=crop&crop=face&q=80',\n    category: 'Bridal',\n    tags: ['bridal', 'elegant', 'classic'],\n    featured: true,\n    status: 'ACTIVE' as const,\n    createdAt: new Date().toISOString(),\n    updatedAt: new Date().toISOString(),\n  },\n  {\n    id: 'fallback-2',\n    title: 'Glamorous Evening Look',\n    description: 'Bold and glamorous makeup for special events',\n    image: 'https://images.unsplash.com/photo-1487412947147-5cebf100ffc2?w=400&h=400&fit=crop&crop=face&q=80',\n    category: 'Party',\n    tags: ['glamour', 'evening', 'bold'],\n    featured: true,\n    status: 'ACTIVE' as const,\n    createdAt: new Date().toISOString(),\n    updatedAt: new Date().toISOString(),\n  },\n  {\n    id: 'fallback-3',\n    title: 'Natural Beauty',\n    description: 'Enhancing natural features with subtle makeup',\n    image: 'https://images.unsplash.com/photo-1616683693504-3ea7e9ad6fec?w=400&h=400&fit=crop&crop=face&q=80',\n    category: 'Natural',\n    tags: ['natural', 'subtle', 'everyday'],\n    featured: true,\n    status: 'ACTIVE' as const,\n    createdAt: new Date().toISOString(),\n    updatedAt: new Date().toISOString(),\n  },\n  {\n    id: 'fallback-4',\n    title: 'Smokey Eyes',\n    description: 'Dramatic smokey eye makeup for evening events',\n    image: 'https://images.unsplash.com/photo-1522337360788-8b13dee7a37e?w=400&h=400&fit=crop&crop=face&q=80',\n    category: 'Evening',\n    tags: ['smokey', 'dramatic', 'eyes'],\n    featured: true,\n    status: 'ACTIVE' as const,\n    createdAt: new Date().toISOString(),\n    updatedAt: new Date().toISOString(),\n  },\n  {\n    id: 'fallback-5',\n    title: 'Traditional Look',\n    description: 'Beautiful traditional makeup for cultural events',\n    image: 'https://images.unsplash.com/photo-1594736797933-d0401ba2fe65?w=400&h=400&fit=crop&crop=face&q=80',\n    category: 'Traditional',\n    tags: ['traditional', 'cultural', 'heritage'],\n    featured: true,\n    status: 'ACTIVE' as const,\n    createdAt: new Date().toISOString(),\n    updatedAt: new Date().toISOString(),\n  },\n  {\n    id: 'fallback-6',\n    title: 'Soft Glam',\n    description: 'Soft and romantic makeup perfect for any occasion',\n    image: 'https://images.unsplash.com/photo-1515377905703-c4788e51af15?w=400&h=400&fit=crop&crop=face&q=80',\n    category: 'Soft Glam',\n    tags: ['soft', 'romantic', 'versatile'],\n    featured: true,\n    status: 'ACTIVE' as const,\n    createdAt: new Date().toISOString(),\n    updatedAt: new Date().toISOString(),\n  },\n  {\n    id: 'fallback-7',\n    title: 'Bold & Beautiful',\n    description: 'Statement makeup with bold colors and dramatic flair',\n    image: 'https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=400&h=400&fit=crop&crop=face&q=80',\n    category: 'Bold',\n    tags: ['bold', 'colorful', 'statement'],\n    featured: true,\n    status: 'ACTIVE' as const,\n    createdAt: new Date().toISOString(),\n    updatedAt: new Date().toISOString(),\n  },\n  {\n    id: 'fallback-8',\n    title: 'Vintage Charm',\n    description: 'Classic vintage-inspired makeup with timeless appeal',\n    image: 'https://images.unsplash.com/photo-1508214751196-bcfd4ca60f91?w=400&h=400&fit=crop&crop=face&q=80',\n    category: 'Vintage',\n    tags: ['vintage', 'classic', 'retro'],\n    featured: true,\n    status: 'ACTIVE' as const,\n    createdAt: new Date().toISOString(),\n    updatedAt: new Date().toISOString(),\n  },\n]\n\nexport default function GalleryShowcase() {\n  const { data: galleryData } = useFeaturedGallery(8)\n\n  // Use API data if available, otherwise use fallback gallery\n  const featuredItems = (galleryData?.gallery && galleryData.gallery.length > 0)\n    ? galleryData.gallery\n    : fallbackGallery\n\n  const isUsingFallback = !galleryData?.gallery || galleryData.gallery.length === 0\n\n  // Always show content - either from API or fallback\n\n  return (\n    <Section id=\"gallery\">\n      <AnimatedElement animation=\"fadeIn\">\n        <SectionHeader\n          subtitle=\"Our Work\"\n          title=\"Portfolio Gallery\"\n          description={isUsingFallback\n            ? \"Explore our stunning portfolio showcasing diverse makeup styles and transformations for various occasions. (Sample gallery - connect to CMS for live portfolio)\"\n            : \"Explore our stunning portfolio showcasing diverse makeup styles and transformations for various occasions.\"\n          }\n        />\n      </AnimatedElement>\n\n      <StaggeredContainer className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-12\">\n        {featuredItems.map((item) => (\n          <StaggeredItem key={item.id}>\n            <div className=\"group relative aspect-square overflow-hidden rounded-xl bg-gradient-to-br from-rose-gold/10 to-blush-pink/10\">\n              <Image\n                src={item.image}\n                alt={item.title}\n                fill\n                className=\"object-cover group-hover:scale-110 transition-transform duration-500\"\n              />\n\n              {/* Overlay */}\n              <div className=\"absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300\">\n                <div className=\"absolute bottom-0 left-0 right-0 p-6 text-white\">\n                  {item.category && (\n                    <Badge variant=\"secondary\" className=\"mb-2 text-xs\">\n                      {item.category}\n                    </Badge>\n                  )}\n                  <h3 className=\"font-display text-lg font-semibold mb-1\">\n                    {item.title}\n                  </h3>\n                  {item.description && (\n                    <p className=\"text-sm text-white/80\">\n                      {item.description}\n                    </p>\n                  )}\n                </div>\n              </div>\n\n              {/* View Icon */}\n              <div className=\"absolute top-4 right-4 w-10 h-10 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300\">\n                <Eye className=\"w-5 h-5 text-white\" />\n              </div>\n\n              {/* Tags */}\n              <div className=\"absolute top-4 left-4 flex flex-wrap gap-1 opacity-0 group-hover:opacity-100 transition-opacity duration-300\">\n                {item.tags.slice(0, 2).map((tag) => (\n                  <Badge key={tag} variant=\"secondary\" className=\"text-xs\">\n                    {tag}\n                  </Badge>\n                ))}\n              </div>\n            </div>\n          </StaggeredItem>\n        ))}\n      </StaggeredContainer>\n\n      {/* Gallery Grid Layout for larger screens */}\n      <div className=\"hidden lg:block mb-12\">\n        <StaggeredContainer className=\"grid grid-cols-4 grid-rows-2 gap-4 h-96\">\n          {featuredItems.slice(0, 6).map((item, index) => {\n            const spanClasses = [\n              'col-span-2 row-span-2', // Large\n              'col-span-1 row-span-1', // Small\n              'col-span-1 row-span-1', // Small\n              'col-span-1 row-span-2', // Tall\n              'col-span-1 row-span-1', // Small\n              'col-span-1 row-span-1', // Small\n            ]\n            \n            return (\n              <StaggeredItem key={`grid-${item.id}`}>\n                <div className={`group relative overflow-hidden rounded-xl bg-gradient-to-br from-rose-gold/10 to-blush-pink/10 ${spanClasses[index]}`}>\n                  <Image\n                    src={`https://images.unsplash.com/photo-1596462502278-27bfdc403348?w=400&h=400&fit=crop&crop=face&q=80`}\n                    alt={item.title}\n                    fill\n                    className=\"object-cover group-hover:scale-110 transition-transform duration-500\"\n                  />\n                  \n                  {/* Overlay */}\n                  <div className=\"absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300\">\n                    <div className=\"absolute bottom-0 left-0 right-0 p-4 text-white\">\n                      <Badge variant=\"secondary\" className=\"mb-2 text-xs\">\n                        {item.category}\n                      </Badge>\n                      <h3 className=\"font-display text-sm font-semibold mb-1\">\n                        {item.title}\n                      </h3>\n                    </div>\n                  </div>\n\n                  {/* View Icon */}\n                  <div className=\"absolute top-3 right-3 w-8 h-8 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300\">\n                    <Eye className=\"w-4 h-4 text-white\" />\n                  </div>\n                </div>\n              </StaggeredItem>\n            )\n          })}\n        </StaggeredContainer>\n      </div>\n\n      <AnimatedElement animation=\"slideUp\" delay={0.6} className=\"text-center\">\n        <Button asChild variant=\"gradient\" size=\"lg\">\n          <Link href=\"/portfolio\">\n            View Full Portfolio\n            <ArrowRight className=\"w-5 h-5 ml-2\" />\n          </Link>\n        </Button>\n      </AnimatedElement>\n    </Section>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;;;AATA;;;;;;;;;AAWA,kDAAkD;AAClD,MAAM,kBAAkB;IACtB;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,OAAO;QACP,UAAU;QACV,MAAM;YAAC;YAAU;YAAW;SAAU;QACtC,UAAU;QACV,QAAQ;QACR,WAAW,IAAI,OAAO,WAAW;QACjC,WAAW,IAAI,OAAO,WAAW;IACnC;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,OAAO;QACP,UAAU;QACV,MAAM;YAAC;YAAW;YAAW;SAAO;QACpC,UAAU;QACV,QAAQ;QACR,WAAW,IAAI,OAAO,WAAW;QACjC,WAAW,IAAI,OAAO,WAAW;IACnC;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,OAAO;QACP,UAAU;QACV,MAAM;YAAC;YAAW;YAAU;SAAW;QACvC,UAAU;QACV,QAAQ;QACR,WAAW,IAAI,OAAO,WAAW;QACjC,WAAW,IAAI,OAAO,WAAW;IACnC;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,OAAO;QACP,UAAU;QACV,MAAM;YAAC;YAAU;YAAY;SAAO;QACpC,UAAU;QACV,QAAQ;QACR,WAAW,IAAI,OAAO,WAAW;QACjC,WAAW,IAAI,OAAO,WAAW;IACnC;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,OAAO;QACP,UAAU;QACV,MAAM;YAAC;YAAe;YAAY;SAAW;QAC7C,UAAU;QACV,QAAQ;QACR,WAAW,IAAI,OAAO,WAAW;QACjC,WAAW,IAAI,OAAO,WAAW;IACnC;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,OAAO;QACP,UAAU;QACV,MAAM;YAAC;YAAQ;YAAY;SAAY;QACvC,UAAU;QACV,QAAQ;QACR,WAAW,IAAI,OAAO,WAAW;QACjC,WAAW,IAAI,OAAO,WAAW;IACnC;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,OAAO;QACP,UAAU;QACV,MAAM;YAAC;YAAQ;YAAY;SAAY;QACvC,UAAU;QACV,QAAQ;QACR,WAAW,IAAI,OAAO,WAAW;QACjC,WAAW,IAAI,OAAO,WAAW;IACnC;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,OAAO;QACP,UAAU;QACV,MAAM;YAAC;YAAW;YAAW;SAAQ;QACrC,UAAU;QACV,QAAQ;QACR,WAAW,IAAI,OAAO,WAAW;QACjC,WAAW,IAAI,OAAO,WAAW;IACnC;CACD;AAEc,SAAS;;IACtB,MAAM,EAAE,MAAM,WAAW,EAAE,GAAG,CAAA,GAAA,6HAAA,CAAA,qBAAkB,AAAD,EAAE;IAEjD,4DAA4D;IAC5D,MAAM,gBAAgB,AAAC,CAAA,wBAAA,kCAAA,YAAa,OAAO,KAAI,YAAY,OAAO,CAAC,MAAM,GAAG,IACxE,YAAY,OAAO,GACnB;IAEJ,MAAM,kBAAkB,EAAC,wBAAA,kCAAA,YAAa,OAAO,KAAI,YAAY,OAAO,CAAC,MAAM,KAAK;IAEhF,oDAAoD;IAEpD,qBACE,6LAAC,sIAAA,CAAA,UAAO;QAAC,IAAG;;0BACV,6LAAC,kJAAA,CAAA,kBAAe;gBAAC,WAAU;0BACzB,cAAA,6LAAC,sIAAA,CAAA,gBAAa;oBACZ,UAAS;oBACT,OAAM;oBACN,aAAa,kBACT,oKACA;;;;;;;;;;;0BAKR,6LAAC,kJAAA,CAAA,qBAAkB;gBAAC,WAAU;0BAC3B,cAAc,GAAG,CAAC,CAAC,qBAClB,6LAAC,kJAAA,CAAA,gBAAa;kCACZ,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,gIAAA,CAAA,UAAK;oCACJ,KAAK,KAAK,KAAK;oCACf,KAAK,KAAK,KAAK;oCACf,IAAI;oCACJ,WAAU;;;;;;8CAIZ,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;4CACZ,KAAK,QAAQ,kBACZ,6LAAC,oIAAA,CAAA,QAAK;gDAAC,SAAQ;gDAAY,WAAU;0DAClC,KAAK,QAAQ;;;;;;0DAGlB,6LAAC;gDAAG,WAAU;0DACX,KAAK,KAAK;;;;;;4CAEZ,KAAK,WAAW,kBACf,6LAAC;gDAAE,WAAU;0DACV,KAAK,WAAW;;;;;;;;;;;;;;;;;8CAOzB,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,mMAAA,CAAA,MAAG;wCAAC,WAAU;;;;;;;;;;;8CAIjB,6LAAC;oCAAI,WAAU;8CACZ,KAAK,IAAI,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,oBAC1B,6LAAC,oIAAA,CAAA,QAAK;4CAAW,SAAQ;4CAAY,WAAU;sDAC5C;2CADS;;;;;;;;;;;;;;;;uBApCA,KAAK,EAAE;;;;;;;;;;0BA+C/B,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,kJAAA,CAAA,qBAAkB;oBAAC,WAAU;8BAC3B,cAAc,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,MAAM;wBACpC,MAAM,cAAc;4BAClB;4BACA;4BACA;4BACA;4BACA;4BACA;yBACD;wBAED,qBACE,6LAAC,kJAAA,CAAA,gBAAa;sCACZ,cAAA,6LAAC;gCAAI,WAAW,AAAC,kGAAoH,OAAnB,WAAW,CAAC,MAAM;;kDAClI,6LAAC,gIAAA,CAAA,UAAK;wCACJ,KAAM;wCACN,KAAK,KAAK,KAAK;wCACf,IAAI;wCACJ,WAAU;;;;;;kDAIZ,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,oIAAA,CAAA,QAAK;oDAAC,SAAQ;oDAAY,WAAU;8DAClC,KAAK,QAAQ;;;;;;8DAEhB,6LAAC;oDAAG,WAAU;8DACX,KAAK,KAAK;;;;;;;;;;;;;;;;;kDAMjB,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,mMAAA,CAAA,MAAG;4CAAC,WAAU;;;;;;;;;;;;;;;;;2BAvBD,AAAC,QAAe,OAAR,KAAK,EAAE;;;;;oBA4BvC;;;;;;;;;;;0BAIJ,6LAAC,kJAAA,CAAA,kBAAe;gBAAC,WAAU;gBAAU,OAAO;gBAAK,WAAU;0BACzD,cAAA,6LAAC,qIAAA,CAAA,SAAM;oBAAC,OAAO;oBAAC,SAAQ;oBAAW,MAAK;8BACtC,cAAA,6LAAC,+JAAA,CAAA,UAAI;wBAAC,MAAK;;4BAAa;0CAEtB,6LAAC,qNAAA,CAAA,aAAU;gCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMlC;GAjIwB;;QACQ,6HAAA,CAAA,qBAAkB;;;KAD1B", "debugId": null}}, {"offset": {"line": 3800, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/anjali-portfolio/anjali/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\nexport interface InputProps\n  extends React.InputHTMLAttributes<HTMLInputElement> {}\n\nconst Input = React.forwardRef<HTMLInputElement, InputProps>(\n  ({ className, type, ...props }, ref) => {\n    return (\n      <input\n        type={type}\n        className={cn(\n          \"flex h-9 w-full rounded-md border border-gray-300 bg-white px-3 py-1 text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-text-primary placeholder:text-text-muted focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-rose-gold focus-visible:border-rose-gold disabled:cursor-not-allowed disabled:opacity-50\",\n          className\n        )}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nInput.displayName = \"Input\"\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAKA,MAAM,sBAAQ,6JAAA,CAAA,aAAgB,MAC5B,QAAgC;QAA/B,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAO;IAC5B,qBACE,6LAAC;QACC,MAAM;QACN,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,2XACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 3837, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/anjali-portfolio/anjali/src/components/ui/textarea.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\nexport interface TextareaProps\n  extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {}\n\nconst Textarea = React.forwardRef<HTMLTextAreaElement, TextareaProps>(\n  ({ className, ...props }, ref) => {\n    return (\n      <textarea\n        className={cn(\n          \"flex min-h-[60px] w-full rounded-md border border-gray-300 bg-white px-3 py-2 text-sm shadow-sm placeholder:text-text-muted focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-rose-gold focus-visible:border-rose-gold disabled:cursor-not-allowed disabled:opacity-50\",\n          className\n        )}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nTextarea.displayName = \"Textarea\"\n\nexport { Textarea }\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAKA,MAAM,yBAAW,6JAAA,CAAA,aAAgB,MAC/B,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;IACtB,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,2RACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,SAAS,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 3873, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/anjali-portfolio/anjali/src/components/forms/contact-form.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { useForm } from 'react-hook-form'\nimport { zodResolver } from '@hookform/resolvers/zod'\nimport { z } from 'zod'\nimport { Send, Phone, MessageCircle } from 'lucide-react'\nimport { Button } from '@/components/ui/button'\nimport { Input } from '@/components/ui/input'\nimport { Textarea } from '@/components/ui/textarea'\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Badge } from '@/components/ui/badge'\nimport { getSiteConfig } from '@/lib/data'\nimport { generateWhatsAppLink } from '@/lib/utils'\n\nconst contactSchema = z.object({\n  name: z.string().min(2, 'Name must be at least 2 characters'),\n  email: z.string().email('Please enter a valid email address'),\n  phone: z.string().min(10, 'Please enter a valid phone number'),\n  service: z.string().min(1, 'Please select a service'),\n  date: z.string().min(1, 'Please select a preferred date'),\n  message: z.string().min(10, 'Message must be at least 10 characters'),\n})\n\ntype ContactFormData = z.infer<typeof contactSchema>\n\nconst services = [\n  'Bridal Makeup',\n  'Party Makeup',\n  'Engagement Makeup',\n  'Traditional Makeup',\n  'Photoshoot Makeup',\n  'Makeup Lessons',\n  'Other'\n]\n\nexport default function ContactForm() {\n  const [isSubmitting, setIsSubmitting] = useState(false)\n  const [isSubmitted, setIsSubmitted] = useState(false)\n  const siteConfig = getSiteConfig()\n\n  const {\n    register,\n    handleSubmit,\n    formState: { errors },\n    reset,\n  } = useForm<ContactFormData>({\n    resolver: zodResolver(contactSchema),\n  })\n\n  const onSubmit = async (data: ContactFormData) => {\n    setIsSubmitting(true)\n    \n    try {\n      // Here you would typically send the data to your backend or EmailJS\n      // For now, we'll simulate a successful submission\n      await new Promise(resolve => setTimeout(resolve, 2000))\n      \n      setIsSubmitted(true)\n      reset()\n    } catch (error) {\n      console.error('Error submitting form:', error)\n    } finally {\n      setIsSubmitting(false)\n    }\n  }\n\n  const whatsappLink = generateWhatsAppLink(\n    siteConfig.contact.whatsapp,\n    siteConfig.whatsappMessage\n  )\n\n  if (isSubmitted) {\n    return (\n      <Card className=\"max-w-md mx-auto text-center\">\n        <CardContent className=\"pt-6\">\n          <div className=\"w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4\">\n            <Send className=\"w-8 h-8 text-green-600\" />\n          </div>\n          <h3 className=\"font-display text-xl font-semibold text-text-primary mb-2\">\n            Message Sent Successfully!\n          </h3>\n          <p className=\"text-text-secondary mb-6\">\n            Thank you for your inquiry. We'll get back to you within 24 hours.\n          </p>\n          <Button \n            onClick={() => setIsSubmitted(false)}\n            variant=\"outline\"\n            className=\"w-full\"\n          >\n            Send Another Message\n          </Button>\n        </CardContent>\n      </Card>\n    )\n  }\n\n  return (\n    <div className=\"grid lg:grid-cols-2 gap-8 max-w-4xl mx-auto\">\n      {/* Contact Form */}\n      <Card>\n        <CardHeader>\n          <CardTitle className=\"font-display text-2xl\">Send us a Message</CardTitle>\n          <CardDescription>\n            Fill out the form below and we'll get back to you as soon as possible.\n          </CardDescription>\n        </CardHeader>\n        <CardContent>\n          <form onSubmit={handleSubmit(onSubmit)} className=\"space-y-4\">\n            <div className=\"grid md:grid-cols-2 gap-4\">\n              <div className=\"space-y-2\">\n                <label htmlFor=\"name\" className=\"text-sm font-medium text-text-primary\">\n                  Full Name *\n                </label>\n                <Input\n                  id=\"name\"\n                  placeholder=\"Your full name\"\n                  {...register('name')}\n                  className={errors.name ? 'border-red-500' : ''}\n                />\n                {errors.name && (\n                  <p className=\"text-red-500 text-xs\">{errors.name.message}</p>\n                )}\n              </div>\n              \n              <div className=\"space-y-2\">\n                <label htmlFor=\"email\" className=\"text-sm font-medium text-text-primary\">\n                  Email Address *\n                </label>\n                <Input\n                  id=\"email\"\n                  type=\"email\"\n                  placeholder=\"<EMAIL>\"\n                  {...register('email')}\n                  className={errors.email ? 'border-red-500' : ''}\n                />\n                {errors.email && (\n                  <p className=\"text-red-500 text-xs\">{errors.email.message}</p>\n                )}\n              </div>\n            </div>\n\n            <div className=\"grid md:grid-cols-2 gap-4\">\n              <div className=\"space-y-2\">\n                <label htmlFor=\"phone\" className=\"text-sm font-medium text-text-primary\">\n                  Phone Number *\n                </label>\n                <Input\n                  id=\"phone\"\n                  placeholder=\"+977-9800000000\"\n                  {...register('phone')}\n                  className={errors.phone ? 'border-red-500' : ''}\n                />\n                {errors.phone && (\n                  <p className=\"text-red-500 text-xs\">{errors.phone.message}</p>\n                )}\n              </div>\n              \n              <div className=\"space-y-2\">\n                <label htmlFor=\"date\" className=\"text-sm font-medium text-text-primary\">\n                  Preferred Date *\n                </label>\n                <Input\n                  id=\"date\"\n                  type=\"date\"\n                  {...register('date')}\n                  className={errors.date ? 'border-red-500' : ''}\n                />\n                {errors.date && (\n                  <p className=\"text-red-500 text-xs\">{errors.date.message}</p>\n                )}\n              </div>\n            </div>\n\n            <div className=\"space-y-2\">\n              <label htmlFor=\"service\" className=\"text-sm font-medium text-text-primary\">\n                Service Interested In *\n              </label>\n              <select\n                id=\"service\"\n                {...register('service')}\n                className={`flex h-9 w-full rounded-md border border-gray-300 bg-white px-3 py-1 text-sm shadow-sm transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-rose-gold focus-visible:border-rose-gold ${\n                  errors.service ? 'border-red-500' : ''\n                }`}\n              >\n                <option value=\"\">Select a service</option>\n                {services.map((service) => (\n                  <option key={service} value={service}>\n                    {service}\n                  </option>\n                ))}\n              </select>\n              {errors.service && (\n                <p className=\"text-red-500 text-xs\">{errors.service.message}</p>\n              )}\n            </div>\n\n            <div className=\"space-y-2\">\n              <label htmlFor=\"message\" className=\"text-sm font-medium text-text-primary\">\n                Message *\n              </label>\n              <Textarea\n                id=\"message\"\n                placeholder=\"Tell us about your requirements, occasion, and any specific preferences...\"\n                rows={4}\n                {...register('message')}\n                className={errors.message ? 'border-red-500' : ''}\n              />\n              {errors.message && (\n                <p className=\"text-red-500 text-xs\">{errors.message.message}</p>\n              )}\n            </div>\n\n            <Button\n              type=\"submit\"\n              variant=\"gradient\"\n              size=\"lg\"\n              className=\"w-full\"\n              disabled={isSubmitting}\n            >\n              {isSubmitting ? (\n                <>\n                  <div className=\"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2\"></div>\n                  Sending...\n                </>\n              ) : (\n                <>\n                  <Send className=\"w-4 h-4 mr-2\" />\n                  Send Message\n                </>\n              )}\n            </Button>\n          </form>\n        </CardContent>\n      </Card>\n\n      {/* Quick Contact Options */}\n      <div className=\"space-y-6\">\n        <Card className=\"bg-gradient-to-br from-rose-gold/10 to-blush-pink/10 border-0\">\n          <CardHeader>\n            <CardTitle className=\"font-display text-xl flex items-center gap-2\">\n              <Phone className=\"w-5 h-5 text-rose-gold-dark\" />\n              Quick Contact\n            </CardTitle>\n            <CardDescription>\n              Need immediate assistance? Contact us directly via WhatsApp or phone.\n            </CardDescription>\n          </CardHeader>\n          <CardContent className=\"space-y-4\">\n            <Button asChild variant=\"gradient\" size=\"lg\" className=\"w-full\">\n              <a href={whatsappLink} target=\"_blank\" rel=\"noopener noreferrer\">\n                <MessageCircle className=\"w-5 h-5 mr-2\" />\n                WhatsApp Now\n              </a>\n            </Button>\n            <Button asChild variant=\"outline\" size=\"lg\" className=\"w-full\">\n              <a href={`tel:${siteConfig.contact.phone}`}>\n                <Phone className=\"w-5 h-5 mr-2\" />\n                Call Now\n              </a>\n            </Button>\n          </CardContent>\n        </Card>\n\n        <Card>\n          <CardHeader>\n            <CardTitle className=\"font-display text-xl\">Business Hours</CardTitle>\n          </CardHeader>\n          <CardContent className=\"space-y-3\">\n            <div className=\"flex justify-between items-center\">\n              <span className=\"text-text-secondary\">Monday - Saturday</span>\n              <Badge variant=\"outline\">9:00 AM - 6:00 PM</Badge>\n            </div>\n            <div className=\"flex justify-between items-center\">\n              <span className=\"text-text-secondary\">Sunday</span>\n              <Badge variant=\"outline\">10:00 AM - 4:00 PM</Badge>\n            </div>\n            <div className=\"pt-2 border-t border-gray-100\">\n              <p className=\"text-sm text-text-muted\">\n                Emergency bookings available with advance notice\n              </p>\n            </div>\n          </CardContent>\n        </Card>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;;;AAbA;;;;;;;;;;;;;AAeA,MAAM,gBAAgB,gLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC7B,MAAM,gLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IACxB,OAAO,gLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,KAAK,CAAC;IACxB,OAAO,gLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,IAAI;IAC1B,SAAS,gLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC3B,MAAM,gLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IACxB,SAAS,gLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,IAAI;AAC9B;AAIA,MAAM,WAAW;IACf;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAEc,SAAS;;IACtB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,aAAa,CAAA,GAAA,qIAAA,CAAA,gBAAa,AAAD;IAE/B,MAAM,EACJ,QAAQ,EACR,YAAY,EACZ,WAAW,EAAE,MAAM,EAAE,EACrB,KAAK,EACN,GAAG,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAmB;QAC3B,UAAU,CAAA,GAAA,iKAAA,CAAA,cAAW,AAAD,EAAE;IACxB;IAEA,MAAM,WAAW,OAAO;QACtB,gBAAgB;QAEhB,IAAI;YACF,oEAAoE;YACpE,kDAAkD;YAClD,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YAEjD,eAAe;YACf;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;QAC1C,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,MAAM,eAAe,CAAA,GAAA,sHAAA,CAAA,uBAAoB,AAAD,EACtC,WAAW,OAAO,CAAC,QAAQ,EAC3B,WAAW,eAAe;IAG5B,IAAI,aAAa;QACf,qBACE,6LAAC,mIAAA,CAAA,OAAI;YAAC,WAAU;sBACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;gBAAC,WAAU;;kCACrB,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,qMAAA,CAAA,OAAI;4BAAC,WAAU;;;;;;;;;;;kCAElB,6LAAC;wBAAG,WAAU;kCAA4D;;;;;;kCAG1E,6LAAC;wBAAE,WAAU;kCAA2B;;;;;;kCAGxC,6LAAC,qIAAA,CAAA,SAAM;wBACL,SAAS,IAAM,eAAe;wBAC9B,SAAQ;wBACR,WAAU;kCACX;;;;;;;;;;;;;;;;;IAMT;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC,mIAAA,CAAA,OAAI;;kCACH,6LAAC,mIAAA,CAAA,aAAU;;0CACT,6LAAC,mIAAA,CAAA,YAAS;gCAAC,WAAU;0CAAwB;;;;;;0CAC7C,6LAAC,mIAAA,CAAA,kBAAe;0CAAC;;;;;;;;;;;;kCAInB,6LAAC,mIAAA,CAAA,cAAW;kCACV,cAAA,6LAAC;4BAAK,UAAU,aAAa;4BAAW,WAAU;;8CAChD,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAM,SAAQ;oDAAO,WAAU;8DAAwC;;;;;;8DAGxE,6LAAC,oIAAA,CAAA,QAAK;oDACJ,IAAG;oDACH,aAAY;oDACX,GAAG,SAAS,OAAO;oDACpB,WAAW,OAAO,IAAI,GAAG,mBAAmB;;;;;;gDAE7C,OAAO,IAAI,kBACV,6LAAC;oDAAE,WAAU;8DAAwB,OAAO,IAAI,CAAC,OAAO;;;;;;;;;;;;sDAI5D,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAM,SAAQ;oDAAQ,WAAU;8DAAwC;;;;;;8DAGzE,6LAAC,oIAAA,CAAA,QAAK;oDACJ,IAAG;oDACH,MAAK;oDACL,aAAY;oDACX,GAAG,SAAS,QAAQ;oDACrB,WAAW,OAAO,KAAK,GAAG,mBAAmB;;;;;;gDAE9C,OAAO,KAAK,kBACX,6LAAC;oDAAE,WAAU;8DAAwB,OAAO,KAAK,CAAC,OAAO;;;;;;;;;;;;;;;;;;8CAK/D,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAM,SAAQ;oDAAQ,WAAU;8DAAwC;;;;;;8DAGzE,6LAAC,oIAAA,CAAA,QAAK;oDACJ,IAAG;oDACH,aAAY;oDACX,GAAG,SAAS,QAAQ;oDACrB,WAAW,OAAO,KAAK,GAAG,mBAAmB;;;;;;gDAE9C,OAAO,KAAK,kBACX,6LAAC;oDAAE,WAAU;8DAAwB,OAAO,KAAK,CAAC,OAAO;;;;;;;;;;;;sDAI7D,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAM,SAAQ;oDAAO,WAAU;8DAAwC;;;;;;8DAGxE,6LAAC,oIAAA,CAAA,QAAK;oDACJ,IAAG;oDACH,MAAK;oDACJ,GAAG,SAAS,OAAO;oDACpB,WAAW,OAAO,IAAI,GAAG,mBAAmB;;;;;;gDAE7C,OAAO,IAAI,kBACV,6LAAC;oDAAE,WAAU;8DAAwB,OAAO,IAAI,CAAC,OAAO;;;;;;;;;;;;;;;;;;8CAK9D,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAM,SAAQ;4CAAU,WAAU;sDAAwC;;;;;;sDAG3E,6LAAC;4CACC,IAAG;4CACF,GAAG,SAAS,UAAU;4CACvB,WAAW,AAAC,wNAEX,OADC,OAAO,OAAO,GAAG,mBAAmB;;8DAGtC,6LAAC;oDAAO,OAAM;8DAAG;;;;;;gDAChB,SAAS,GAAG,CAAC,CAAC,wBACb,6LAAC;wDAAqB,OAAO;kEAC1B;uDADU;;;;;;;;;;;wCAKhB,OAAO,OAAO,kBACb,6LAAC;4CAAE,WAAU;sDAAwB,OAAO,OAAO,CAAC,OAAO;;;;;;;;;;;;8CAI/D,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAM,SAAQ;4CAAU,WAAU;sDAAwC;;;;;;sDAG3E,6LAAC,uIAAA,CAAA,WAAQ;4CACP,IAAG;4CACH,aAAY;4CACZ,MAAM;4CACL,GAAG,SAAS,UAAU;4CACvB,WAAW,OAAO,OAAO,GAAG,mBAAmB;;;;;;wCAEhD,OAAO,OAAO,kBACb,6LAAC;4CAAE,WAAU;sDAAwB,OAAO,OAAO,CAAC,OAAO;;;;;;;;;;;;8CAI/D,6LAAC,qIAAA,CAAA,SAAM;oCACL,MAAK;oCACL,SAAQ;oCACR,MAAK;oCACL,WAAU;oCACV,UAAU;8CAET,6BACC;;0DACE,6LAAC;gDAAI,WAAU;;;;;;4CAA0F;;qEAI3G;;0DACE,6LAAC,qMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;;;;;;;;;;;;;;0BAU7C,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,mIAAA,CAAA,OAAI;wBAAC,WAAU;;0CACd,6LAAC,mIAAA,CAAA,aAAU;;kDACT,6LAAC,mIAAA,CAAA,YAAS;wCAAC,WAAU;;0DACnB,6LAAC,uMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;4CAAgC;;;;;;;kDAGnD,6LAAC,mIAAA,CAAA,kBAAe;kDAAC;;;;;;;;;;;;0CAInB,6LAAC,mIAAA,CAAA,cAAW;gCAAC,WAAU;;kDACrB,6LAAC,qIAAA,CAAA,SAAM;wCAAC,OAAO;wCAAC,SAAQ;wCAAW,MAAK;wCAAK,WAAU;kDACrD,cAAA,6LAAC;4CAAE,MAAM;4CAAc,QAAO;4CAAS,KAAI;;8DACzC,6LAAC,2NAAA,CAAA,gBAAa;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;;;;;kDAI9C,6LAAC,qIAAA,CAAA,SAAM;wCAAC,OAAO;wCAAC,SAAQ;wCAAU,MAAK;wCAAK,WAAU;kDACpD,cAAA,6LAAC;4CAAE,MAAM,AAAC,OAA+B,OAAzB,WAAW,OAAO,CAAC,KAAK;;8DACtC,6LAAC,uMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;;;;;;;;;;;;;;;;;kCAO1C,6LAAC,mIAAA,CAAA,OAAI;;0CACH,6LAAC,mIAAA,CAAA,aAAU;0CACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;oCAAC,WAAU;8CAAuB;;;;;;;;;;;0CAE9C,6LAAC,mIAAA,CAAA,cAAW;gCAAC,WAAU;;kDACrB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAK,WAAU;0DAAsB;;;;;;0DACtC,6LAAC,oIAAA,CAAA,QAAK;gDAAC,SAAQ;0DAAU;;;;;;;;;;;;kDAE3B,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAK,WAAU;0DAAsB;;;;;;0DACtC,6LAAC,oIAAA,CAAA,QAAK;gDAAC,SAAQ;0DAAU;;;;;;;;;;;;kDAE3B,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAE,WAAU;sDAA0B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASrD;GA3PwB;;QAUlB,iKAAA,CAAA,UAAO;;;KAVW", "debugId": null}}, {"offset": {"line": 4577, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/anjali-portfolio/anjali/src/components/sections/contact-section.tsx"], "sourcesContent": ["'use client'\n\nimport { Section, SectionHeader } from '@/components/ui/section'\nimport { AnimatedElement } from '@/components/ui/animated-element'\nimport ContactForm from '@/components/forms/contact-form'\n\nexport default function ContactSection() {\n  return (\n    <Section background=\"cream\" id=\"contact\">\n      <AnimatedElement animation=\"fadeIn\">\n        <SectionHeader\n          subtitle=\"Get In Touch\"\n          title=\"Ready to Book Your Session?\"\n          description=\"Contact us today to schedule your makeup consultation and let us help you look and feel your absolute best.\"\n        />\n      </AnimatedElement>\n\n      <AnimatedElement animation=\"slideUp\" delay={0.3}>\n        <ContactForm />\n      </AnimatedElement>\n    </Section>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAMe,SAAS;IACtB,qBACE,6LAAC,sIAAA,CAAA,UAAO;QAAC,YAAW;QAAQ,IAAG;;0BAC7B,6LAAC,kJAAA,CAAA,kBAAe;gBAAC,WAAU;0BACzB,cAAA,6LAAC,sIAAA,CAAA,gBAAa;oBACZ,UAAS;oBACT,OAAM;oBACN,aAAY;;;;;;;;;;;0BAIhB,6LAAC,kJAAA,CAAA,kBAAe;gBAAC,WAAU;gBAAU,OAAO;0BAC1C,cAAA,6LAAC,iJAAA,CAAA,UAAW;;;;;;;;;;;;;;;;AAIpB;KAhBwB", "debugId": null}}]}