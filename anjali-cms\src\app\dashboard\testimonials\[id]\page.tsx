"use client"

import { useState, useEffect } from 'react'
import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation'
import Link from 'next/link'
import Image from 'next/image'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Separator } from '@/components/ui/separator'
import { Edit, ArrowLeft, Calendar, Mail, Star, Check, X } from 'lucide-react'
import { formatDate } from '@/lib/utils'
import { toast } from 'sonner'

interface Testimonial {
  id: string
  name: string
  email?: string
  message: string
  rating: number
  image?: string
  service?: string
  status: 'PENDING' | 'APPROVED' | 'REJECTED'
  createdAt: string
  updatedAt: string
}

export default function TestimonialViewPage() {
  const params = useParams()
  const router = useRouter()
  const [testimonial, setTestimonial] = useState<Testimonial | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const fetchTestimonial = async () => {
      try {
        const response = await fetch(`/api/testimonials/${params.id}`)
        if (response.ok) {
          const data = await response.json()
          setTestimonial(data)
        } else {
          toast.error('Testimonial not found')
          router.push('/dashboard/testimonials')
        }
      } catch (error) {
        toast.error('Error fetching testimonial')
        router.push('/dashboard/testimonials')
      } finally {
        setLoading(false)
      }
    }

    if (params.id) {
      fetchTestimonial()
    }
  }, [params.id, router])

  const updateStatus = async (status: 'APPROVED' | 'REJECTED') => {
    try {
      const response = await fetch(`/api/testimonials/${params.id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ status }),
      })

      if (response.ok) {
        toast.success(`Testimonial ${status.toLowerCase()} successfully`)
        setTestimonial(prev => prev ? { ...prev, status } : null)
      } else {
        toast.error('Failed to update testimonial')
      }
    } catch (error) {
      toast.error('Error updating testimonial')
    }
  }

  if (loading) {
    return <div>Loading...</div>
  }

  if (!testimonial) {
    return <div>Testimonial not found</div>
  }

  const getStatusBadge = (status: string) => {
    const variants = {
      PENDING: 'secondary',
      APPROVED: 'default',
      REJECTED: 'destructive',
    } as const

    return (
      <Badge variant={variants[status as keyof typeof variants]}>
        {status}
      </Badge>
    )
  }

  const renderStars = (rating: number) => {
    return (
      <div className="flex">
        {[1, 2, 3, 4, 5].map((star) => (
          <Star
            key={star}
            className={`h-5 w-5 ${
              star <= rating ? 'text-yellow-400 fill-current' : 'text-gray-300'
            }`}
          />
        ))}
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="ghost" size="sm" onClick={() => router.back()}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back
          </Button>
          <div>
            <h1 className="text-3xl font-bold">Testimonial from {testimonial.name}</h1>
            <div className="flex items-center gap-4 text-sm text-muted-foreground mt-2">
              <div className="flex items-center gap-1">
                <Calendar className="h-4 w-4" />
                {formatDate(testimonial.createdAt)}
              </div>
              {testimonial.email && (
                <div className="flex items-center gap-1">
                  <Mail className="h-4 w-4" />
                  {testimonial.email}
                </div>
              )}
            </div>
          </div>
        </div>
        <div className="flex items-center gap-2">
          {getStatusBadge(testimonial.status)}
          {testimonial.service && (
            <Badge variant="outline">{testimonial.service}</Badge>
          )}
          {testimonial.status !== 'APPROVED' && (
            <Button size="sm" onClick={() => updateStatus('APPROVED')}>
              <Check className="h-4 w-4 mr-2" />
              Approve
            </Button>
          )}
          {testimonial.status !== 'REJECTED' && (
            <Button size="sm" variant="outline" onClick={() => updateStatus('REJECTED')}>
              <X className="h-4 w-4 mr-2" />
              Reject
            </Button>
          )}
          <Button asChild>
            <Link href={`/dashboard/testimonials/${testimonial.id}/edit`}>
              <Edit className="h-4 w-4 mr-2" />
              Edit
            </Link>
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div className="lg:col-span-2 space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Testimonial Message</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center gap-2">
                  <span className="text-sm font-medium">Rating:</span>
                  {renderStars(testimonial.rating)}
                  <span className="text-sm text-muted-foreground">
                    ({testimonial.rating}/5)
                  </span>
                </div>
                <blockquote className="border-l-4 border-primary pl-4 italic text-lg leading-relaxed">
                  "{testimonial.message}"
                </blockquote>
                <div className="text-right">
                  <p className="font-medium">— {testimonial.name}</p>
                  {testimonial.service && (
                    <p className="text-sm text-muted-foreground">
                      {testimonial.service}
                    </p>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        <div className="space-y-6">
          {testimonial.image && (
            <Card>
              <CardHeader>
                <CardTitle>Client Photo</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="relative aspect-square w-full max-w-sm mx-auto overflow-hidden rounded-lg">
                  <Image
                    src={testimonial.image}
                    alt={testimonial.name}
                    fill
                    className="object-cover"
                  />
                </div>
              </CardContent>
            </Card>
          )}

          <Card>
            <CardHeader>
              <CardTitle>Details</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label className="text-sm font-medium">Status</Label>
                <div className="mt-1">
                  {getStatusBadge(testimonial.status)}
                </div>
              </div>

              <div>
                <Label className="text-sm font-medium">Client Name</Label>
                <p className="text-sm text-muted-foreground mt-1">
                  {testimonial.name}
                </p>
              </div>

              {testimonial.email && (
                <div>
                  <Label className="text-sm font-medium">Email</Label>
                  <p className="text-sm text-muted-foreground mt-1">
                    {testimonial.email}
                  </p>
                </div>
              )}

              {testimonial.service && (
                <div>
                  <Label className="text-sm font-medium">Service</Label>
                  <div className="mt-1">
                    <Badge variant="outline">{testimonial.service}</Badge>
                  </div>
                </div>
              )}

              <div>
                <Label className="text-sm font-medium">Rating</Label>
                <div className="mt-1 flex items-center gap-2">
                  {renderStars(testimonial.rating)}
                  <span className="text-sm text-muted-foreground">
                    {testimonial.rating}/5
                  </span>
                </div>
              </div>

              <Separator />

              <div className="space-y-2 text-sm">
                <div>
                  <span className="font-medium">Created:</span>{' '}
                  {formatDate(testimonial.createdAt)}
                </div>
                <div>
                  <span className="font-medium">Updated:</span>{' '}
                  {formatDate(testimonial.updatedAt)}
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Quick Actions</CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              {testimonial.status === 'PENDING' && (
                <>
                  <Button 
                    className="w-full" 
                    onClick={() => updateStatus('APPROVED')}
                  >
                    <Check className="h-4 w-4 mr-2" />
                    Approve Testimonial
                  </Button>
                  <Button 
                    variant="outline" 
                    className="w-full"
                    onClick={() => updateStatus('REJECTED')}
                  >
                    <X className="h-4 w-4 mr-2" />
                    Reject Testimonial
                  </Button>
                </>
              )}
              <Button asChild className="w-full" variant="outline">
                <Link href={`/dashboard/testimonials/${testimonial.id}/edit`}>
                  <Edit className="h-4 w-4 mr-2" />
                  Edit Testimonial
                </Link>
              </Button>
              <Button variant="outline" className="w-full" asChild>
                <Link href="/dashboard/testimonials">
                  View All Testimonials
                </Link>
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}

function Label({ children, className }: { children: React.ReactNode; className?: string }) {
  return <div className={className}>{children}</div>
}
