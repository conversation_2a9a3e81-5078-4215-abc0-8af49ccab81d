# Cloudinary Setup Guide

## 🚨 Error: "Upload preset must be whitelisted for unsigned uploads"

This error occurs because Cloudinary requires upload presets to be configured for unsigned uploads. Follow this guide to fix it.

## 🔧 Step-by-Step Setup

### 1. **Login to Cloudinary Dashboard**
- Go to [cloudinary.com](https://cloudinary.com)
- Login to your account
- Navigate to your dashboard

### 2. **Create Upload Preset**

#### Option A: Quick Setup (Recommended)
1. Go to **Settings** → **Upload** → **Upload presets**
2. Click **"Add upload preset"**
3. Configure the preset:
   ```
   Preset name: anjali_cms_unsigned
   Signing mode: Unsigned
   Folder: anjali-cms
   ```
4. **Advanced settings:**
   ```
   Resource type: Image
   Access mode: Public
   Allowed formats: jpg, jpeg, png, gif, webp
   Max file size: 5MB (5000000 bytes)
   Auto backup: Enabled
   Overwrite: False
   Unique filename: True
   ```
5. Click **"Save"**

#### Option B: Manual Configuration
1. Go to **Settings** → **Upload**
2. Scroll to **"Upload presets"**
3. Click **"Add upload preset"**
4. Fill in the details:

**Basic Settings:**
- **Preset name**: `anjali_cms_unsigned`
- **Signing mode**: `Unsigned` ⚠️ **IMPORTANT**
- **Use filename**: `True`
- **Unique filename**: `True`
- **Folder**: `anjali-cms`

**Media Analysis:**
- **Resource type**: `Image`
- **Access mode**: `Public`
- **Delivery type**: `Upload`

**Upload Control:**
- **Allowed formats**: `jpg,jpeg,png,gif,webp`
- **Max file size**: `5000000` (5MB)
- **Max image width**: `2000`
- **Max image height**: `2000`

**Upload Manipulations:**
- **Quality**: `auto`
- **Format**: `auto`

### 3. **Update Environment Variables**

Add to your `.env.local` file:

```env
# Cloudinary Configuration
NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME="your-cloud-name"
NEXT_PUBLIC_CLOUDINARY_UPLOAD_PRESET="anjali_cms_unsigned"
CLOUDINARY_API_KEY="your-api-key"
CLOUDINARY_API_SECRET="your-api-secret"
```

**Find your credentials:**
1. Go to **Dashboard** → **Account Details**
2. Copy your **Cloud name**, **API Key**, and **API Secret**

### 4. **Verify Setup**

Test the upload preset:
1. Go to **Media Library** → **Upload**
2. Try uploading an image
3. Check if it appears in the `anjali-cms` folder

## 🔍 Troubleshooting

### Common Issues:

#### **1. "Upload preset not found"**
- ✅ Check preset name spelling: `anjali_cms_unsigned`
- ✅ Ensure preset is saved in Cloudinary dashboard
- ✅ Verify environment variable is correct

#### **2. "Signing mode must be unsigned"**
- ✅ Go to preset settings
- ✅ Change **Signing mode** to **"Unsigned"**
- ✅ Save the preset

#### **3. "Invalid cloud name"**
- ✅ Check `NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME` in `.env.local`
- ✅ Ensure it matches your Cloudinary account
- ✅ No spaces or special characters

#### **4. "Upload failed"**
- ✅ Check file size (max 5MB)
- ✅ Verify file format (jpg, png, gif, webp)
- ✅ Check internet connection

### Debug Steps:

1. **Check Environment Variables:**
   ```bash
   echo $NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME
   echo $NEXT_PUBLIC_CLOUDINARY_UPLOAD_PRESET
   ```

2. **Test Upload Preset:**
   - Go to Cloudinary dashboard
   - Try manual upload with the preset
   - Check if files appear in correct folder

3. **Browser Console:**
   - Open browser dev tools
   - Check for error messages
   - Look for network request failures

## 🎯 Alternative: Signed Uploads

If you prefer signed uploads (more secure):

### 1. **Create Signed Preset**
```
Preset name: anjali_cms_signed
Signing mode: Signed
```

### 2. **Update Component**
```typescript
// In image-upload.tsx
uploadPreset="anjali_cms_signed"
```

### 3. **Add API Route**
Create `/api/cloudinary-signature.ts` for server-side signing.

## 📁 Folder Structure

Your uploads will be organized as:
```
cloudinary.com/your-cloud/
├── anjali-cms/
│   ├── blogs/
│   ├── services/
│   ├── packages/
│   ├── gallery/
│   └── testimonials/
```

## 🔐 Security Best Practices

### For Production:
1. **Use signed uploads** for sensitive content
2. **Restrict upload sources** to your domain
3. **Set file size limits** appropriately
4. **Enable moderation** for user uploads
5. **Use transformation URLs** for optimized delivery

### Upload Restrictions:
```
Max file size: 5MB
Allowed formats: jpg, jpeg, png, gif, webp
Max dimensions: 2000x2000px
Folder: anjali-cms
```

## 🧪 Test Upload

After setup, test with this code in browser console:

```javascript
// Test if Cloudinary is configured correctly
console.log('Cloud Name:', process.env.NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME)
console.log('Upload Preset:', process.env.NEXT_PUBLIC_CLOUDINARY_UPLOAD_PRESET)
```

## 📞 Support

If you still have issues:
1. **Check Cloudinary Status**: [status.cloudinary.com](https://status.cloudinary.com)
2. **Review Documentation**: [cloudinary.com/documentation](https://cloudinary.com/documentation)
3. **Contact Support**: Through Cloudinary dashboard

---

**✅ Once configured, your image uploads will work seamlessly!**
