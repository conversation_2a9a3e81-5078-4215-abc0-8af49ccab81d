import { Metadata } from 'next'
import ServicesHero from '@/components/sections/services-hero'
import ServicesGrid from '@/components/sections/services-grid'
import ServicesCTA from '@/components/sections/services-cta'
import { getSiteConfig } from '@/lib/data'

const siteConfig = getSiteConfig()

export const metadata: Metadata = {
  title: `Professional Makeup Services | ${siteConfig.site.name}`,
  description: 'Comprehensive makeup services including bridal, party, traditional, and photoshoot makeup. Professional artistry for all your special occasions in Nepal.',
  openGraph: {
    title: `Professional Makeup Services | ${siteConfig.site.name}`,
    description: 'Comprehensive makeup services including bridal, party, traditional, and photoshoot makeup. Professional artistry for all your special occasions in Nepal.',
    url: `${siteConfig.site.url}/services`,
  },
}

export default function ServicesPage() {
  return (
    <>
      <ServicesHero />
      <ServicesGrid />
      <ServicesCTA />
    </>
  )
}
