import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

const bulkActionSchema = z.object({
  ids: z.array(z.string()).min(1, 'At least one item must be selected'),
  action: z.enum(['feature', 'unfeature', 'activate', 'deactivate', 'archive', 'delete']),
})

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const { ids, action } = bulkActionSchema.parse(body)

    let result
    let message

    switch (action) {
      case 'feature':
        result = await prisma.gallery.updateMany({
          where: { id: { in: ids } },
          data: { featured: true },
        })
        message = `${result.count} item(s) marked as featured`
        break

      case 'unfeature':
        result = await prisma.gallery.updateMany({
          where: { id: { in: ids } },
          data: { featured: false },
        })
        message = `${result.count} item(s) unmarked as featured`
        break

      case 'activate':
        result = await prisma.gallery.updateMany({
          where: { id: { in: ids } },
          data: { status: 'ACTIVE' },
        })
        message = `${result.count} item(s) activated`
        break

      case 'deactivate':
        result = await prisma.gallery.updateMany({
          where: { id: { in: ids } },
          data: { status: 'INACTIVE' },
        })
        message = `${result.count} item(s) deactivated`
        break

      case 'archive':
        result = await prisma.gallery.updateMany({
          where: { id: { in: ids } },
          data: { status: 'ARCHIVED' },
        })
        message = `${result.count} item(s) archived`
        break

      case 'delete':
        result = await prisma.gallery.deleteMany({
          where: { id: { in: ids } },
        })
        message = `${result.count} item(s) deleted`
        break

      default:
        return NextResponse.json({ error: 'Invalid action' }, { status: 400 })
    }

    return NextResponse.json({ 
      message,
      count: result.count 
    })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json({ error: error.errors }, { status: 400 })
    }
    
    console.error('Error performing bulk action:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
