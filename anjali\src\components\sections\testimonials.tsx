'use client'

import Image from 'next/image'
import { <PERSON>, Quote, ChevronLeft, ChevronRight } from 'lucide-react'
import { Card, CardContent } from '@/components/ui/card'
import { Section, SectionHeader } from '@/components/ui/section'
import { AnimatedElement, StaggeredContainer, StaggeredItem } from '@/components/ui/animated-element'
import { useApprovedTestimonials } from '@/hooks/use-api'
import { formatDate } from '@/lib/utils'
import { But<PERSON> } from '@/components/ui/button'
import { useState } from 'react'

// Fallback testimonials when API is not available
const fallbackTestimonials = [
  {
    id: 'fallback-1',
    name: '<PERSON>',
    email: '<EMAIL>',
    message: '<PERSON> brings military precision and security expertise to our high-profile transportation services. His vigilance and professionalism ensure the safety of our most important clients.',
    rating: 5,
    service: 'Security Specialist',
    specialties: ['Executive Security', 'Risk Assessment', 'Protocol Management'],
    image: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=400&h=400&fit=crop&crop=face&q=80',
    status: 'APPROVED' as const,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
  {
    id: 'fallback-2',
    name: 'Sarah <PERSON>',
    message: 'Sarah has transformed our client experience with her attention to detail and exceptional service standards. Her dedication to excellence is unmatched.',
    rating: 5,
    service: 'Client Relations Manager',
    specialties: ['Customer Service', 'Team Leadership', 'Process Optimization'],
    image: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=400&h=400&fit=crop&crop=face&q=80',
    status: 'APPROVED' as const,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
  {
    id: 'fallback-3',
    name: 'Michael Chen',
    email: '<EMAIL>',
    message: 'Michael\'s technical expertise and innovative solutions have revolutionized our operations. His ability to solve complex problems is truly remarkable.',
    rating: 5,
    service: 'Technical Director',
    specialties: ['System Architecture', 'Innovation', 'Problem Solving'],
    image: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=400&h=400&fit=crop&crop=face&q=80',
    status: 'APPROVED' as const,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
]

export default function Testimonials() {
  const { data: testimonialsData } = useApprovedTestimonials(6)
  const [currentIndex, setCurrentIndex] = useState(0)

  // Use API data if available, otherwise use fallback testimonials
  const testimonials = (testimonialsData?.testimonials && testimonialsData.testimonials.length > 0)
    ? testimonialsData.testimonials
    : fallbackTestimonials

  const isUsingFallback = !testimonialsData?.testimonials || testimonialsData.testimonials.length === 0

  const nextTestimonial = () => {
    setCurrentIndex((prev) => (prev + 1) % testimonials.length)
  }

  const prevTestimonial = () => {
    setCurrentIndex((prev) => (prev - 1 + testimonials.length) % testimonials.length)
  }

  const currentTestimonial = testimonials[currentIndex]

  return (
    <Section background="gradient" id="testimonials">
      <AnimatedElement animation="fadeIn">
        <SectionHeader
          subtitle="Client Reviews"
          title="What Our Clients Say"
          description={isUsingFallback
            ? "Read what our satisfied clients have to say about their experience with our professional makeup services. (Sample reviews - connect to CMS for live testimonials)"
            : "Read what our satisfied clients have to say about their experience with our professional makeup services."
          }
        />
      </AnimatedElement>

      {/* Main Testimonial Display */}
      <AnimatedElement animation="slideUp" delay={0.2} className="mt-16">
        <div className="relative max-w-6xl mx-auto">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            {/* Large Profile Image */}
            <div className="relative">
              <div className="relative w-full aspect-[4/5] rounded-2xl overflow-hidden bg-gradient-to-br from-rose-gold to-blush-pink">
                <Image
                  src={currentTestimonial.image || `https://images.unsplash.com/photo-1494790108755-2616b612b786?w=600&h=750&fit=crop&crop=face&q=80`}
                  alt={currentTestimonial.name}
                  fill
                  className="object-cover"
                />
              </div>
            </div>

            {/* Testimonial Content */}
            <div className="space-y-8">
              {/* Large Quote Mark */}
              <div className="text-6xl text-rose-gold/30 font-serif leading-none">
                "
              </div>

              {/* Client Name and Service */}
              <div className="space-y-2">
                <h3 className="text-2xl font-bold text-text-primary">
                  {currentTestimonial.name}
                </h3>
                {currentTestimonial.service && (
                  <p className="text-blue-600 font-medium">
                    {currentTestimonial.service}
                  </p>
                )}
              </div>

              {/* Testimonial Text */}
              <blockquote className="text-lg text-text-secondary leading-relaxed">
                {currentTestimonial.message}
              </blockquote>

              {/* Navigation Dots */}
              <div className="flex items-center gap-2">
                {testimonials.map((_, index) => (
                  <button
                    key={index}
                    onClick={() => setCurrentIndex(index)}
                    className={`w-3 h-3 rounded-full transition-colors ${
                      index === currentIndex
                        ? 'bg-blue-600'
                        : 'bg-gray-300 hover:bg-gray-400'
                    }`}
                  />
                ))}
              </div>
            </div>
          </div>

          {/* Navigation Arrows */}
          <button
            onClick={prevTestimonial}
            className="absolute left-4 top-1/2 -translate-y-1/2 w-12 h-12 bg-white/80 backdrop-blur-sm rounded-full shadow-lg hover:shadow-xl transition-all duration-300 flex items-center justify-center text-text-primary hover:bg-white"
          >
            <ChevronLeft className="w-6 h-6" />
          </button>

          <button
            onClick={nextTestimonial}
            className="absolute right-4 top-1/2 -translate-y-1/2 w-12 h-12 bg-white/80 backdrop-blur-sm rounded-full shadow-lg hover:shadow-xl transition-all duration-300 flex items-center justify-center text-text-primary hover:bg-white"
          >
            <ChevronRight className="w-6 h-6" />
          </button>
        </div>
      </AnimatedElement>

      {/* Stats Section */}
      <AnimatedElement animation="slideUp" delay={0.8} className="mt-24">
        <div className="grid grid-cols-2 md:grid-cols-4 gap-8 text-center">
          <div className="space-y-2">
            <div className="text-3xl md:text-4xl font-bold text-text-primary">50+</div>
            <div className="text-text-secondary">Happy Clients</div>
          </div>
          <div className="space-y-2">
            <div className="text-3xl md:text-4xl font-bold text-text-primary">5.0</div>
            <div className="text-text-secondary">Average Rating</div>
          </div>
          <div className="space-y-2">
            <div className="text-3xl md:text-4xl font-bold text-text-primary">100+</div>
            <div className="text-text-secondary">Makeup Sessions</div>
          </div>
          <div className="space-y-2">
            <div className="text-3xl md:text-4xl font-bold text-text-primary">7</div>
            <div className="text-text-secondary">Cities Served</div>
          </div>
        </div>
      </AnimatedElement>
    </Section>
  )
}
