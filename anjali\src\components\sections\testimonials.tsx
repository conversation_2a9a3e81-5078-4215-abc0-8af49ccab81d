'use client'

import Image from 'next/image'
import { <PERSON>, Quote, ChevronLeft, ChevronRight } from 'lucide-react'
import { Card, CardContent } from '@/components/ui/card'
import { Section, SectionHeader } from '@/components/ui/section'
import { AnimatedElement, StaggeredContainer, StaggeredItem } from '@/components/ui/animated-element'
import { useApprovedTestimonials } from '@/hooks/use-api'
import { formatDate } from '@/lib/utils'
import { Button } from '@/components/ui/button'
import { useState } from 'react'

// Fallback testimonials when API is not available
const fallbackTestimonials = [
  {
    id: 'fallback-1',
    name: '<PERSON><PERSON>',
    email: '<EMAIL>',
    message: '<PERSON><PERSON><PERSON> did an amazing job on my wedding day! The makeup was flawless and lasted the entire day. I felt like a princess. Highly recommended for all brides!',
    rating: 5,
    service: 'Bridal Makeup',
    specialties: ['Bridal Look', 'Traditional Style', 'Long-lasting'],
    image: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=400&h=400&fit=crop&crop=face&q=80',
    status: 'APPROVED' as const,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
  {
    id: 'fallback-2',
    name: 'Sita Rai',
    message: 'Perfect makeup for my engagement party. Anjali understood exactly what I wanted and delivered beyond my expectations. The team is professional and talented.',
    rating: 5,
    service: 'Party Makeup',
    specialties: ['Party Look', 'Modern Style', 'Professional'],
    image: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=400&h=400&fit=crop&crop=face&q=80',
    status: 'APPROVED' as const,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
  {
    id: 'fallback-3',
    name: 'Kamala Thapa',
    email: '<EMAIL>',
    message: 'I had my makeup done for a traditional ceremony and it was absolutely beautiful. Anjali respects cultural traditions while adding her modern touch.',
    rating: 5,
    service: 'Traditional Makeup',
    specialties: ['Cultural Traditions', 'Modern Touch', 'Ceremony Ready'],
    image: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=400&h=400&fit=crop&crop=face&q=80',
    status: 'APPROVED' as const,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
]

export default function Testimonials() {
  const { data: testimonialsData } = useApprovedTestimonials(6)
  const [currentIndex, setCurrentIndex] = useState(0)

  // Use API data if available, otherwise use fallback testimonials
  const testimonials = (testimonialsData?.testimonials && testimonialsData.testimonials.length > 0)
    ? testimonialsData.testimonials
    : fallbackTestimonials

  const isUsingFallback = !testimonialsData?.testimonials || testimonialsData.testimonials.length === 0

  const nextTestimonial = () => {
    setCurrentIndex((prev) => (prev + 1) % testimonials.length)
  }

  const prevTestimonial = () => {
    setCurrentIndex((prev) => (prev - 1 + testimonials.length) % testimonials.length)
  }

  const currentTestimonial = testimonials[currentIndex]

  return (
    <Section background="gradient" id="testimonials">
      <AnimatedElement animation="fadeIn">
        <SectionHeader
          subtitle="Client Reviews"
          title="What Our Clients Say"
          description={isUsingFallback
            ? "Read what our satisfied clients have to say about their experience with our professional makeup services. (Sample reviews - connect to CMS for live testimonials)"
            : "Read what our satisfied clients have to say about their experience with our professional makeup services."
          }
        />
      </AnimatedElement>

      {/* Main Testimonial Display */}
      <AnimatedElement animation="slideUp" delay={0.2} className="mt-16">
        <div className="relative max-w-6xl mx-auto">
          <div className="grid lg:grid-cols-2 gap-8 lg:gap-12 items-center">
            {/* Large Profile Image */}
            <div className="relative order-2 lg:order-1">
              <div className="relative w-full aspect-[3/4] lg:aspect-[4/5] max-w-sm mx-auto lg:max-w-none rounded-2xl overflow-hidden bg-gradient-to-br from-rose-gold to-blush-pink">
                <Image
                  src={currentTestimonial.image || `https://images.unsplash.com/photo-1494790108755-2616b612b786?w=600&h=750&fit=crop&crop=face&q=80`}
                  alt={currentTestimonial.name}
                  fill
                  className="object-cover"
                />
              </div>
            </div>

            {/* Testimonial Content */}
            <div className="space-y-6 lg:space-y-8 order-1 lg:order-2 text-center lg:text-left">
              {/* Large Quote Mark */}
              <div className="text-4xl lg:text-6xl text-rose-gold/30 font-serif leading-none">
                "
              </div>

              {/* Client Name and Service */}
              <div className="space-y-2">
                <h3 className="text-xl lg:text-2xl font-bold text-text-primary">
                  {currentTestimonial.name}
                </h3>
                {currentTestimonial.service && (
                  <p className="text-blue-600 font-medium">
                    {currentTestimonial.service}
                  </p>
                )}
              </div>

              {/* Testimonial Text */}
              <blockquote className="text-base lg:text-lg text-text-secondary leading-relaxed">
                {currentTestimonial.message}
              </blockquote>

              {/* Specialties */}
              {currentTestimonial.specialties && (
                <div className="space-y-3">
                  <h4 className="text-sm font-semibold text-text-primary">Specialties:</h4>
                  <div className="flex flex-wrap gap-2 justify-center lg:justify-start">
                    {currentTestimonial.specialties.map((specialty, index) => (
                      <span
                        key={index}
                        className="px-3 py-1 bg-blue-600 text-white text-sm rounded-full font-medium"
                      >
                        {specialty}
                      </span>
                    ))}
                  </div>
                </div>
              )}

              {/* Navigation Dots */}
              <div className="flex items-center justify-center gap-2">
                {testimonials.map((_, index) => (
                  <button
                    key={index}
                    onClick={() => setCurrentIndex(index)}
                    className={`w-3 h-3 rounded-full transition-colors ${
                      index === currentIndex
                        ? 'bg-blue-600'
                        : 'bg-gray-300 hover:bg-gray-400'
                    }`}
                  />
                ))}
              </div>

              {/* Mobile Navigation Buttons */}
              <div className="flex items-center justify-center gap-4 lg:hidden">
                <button
                  onClick={prevTestimonial}
                  className="w-10 h-10 bg-white/80 backdrop-blur-sm rounded-full shadow-lg hover:shadow-xl transition-all duration-300 flex items-center justify-center text-text-primary hover:bg-white"
                >
                  <ChevronLeft className="w-5 h-5" />
                </button>

                <button
                  onClick={nextTestimonial}
                  className="w-10 h-10 bg-white/80 backdrop-blur-sm rounded-full shadow-lg hover:shadow-xl transition-all duration-300 flex items-center justify-center text-text-primary hover:bg-white"
                >
                  <ChevronRight className="w-5 h-5" />
                </button>
              </div>
            </div>
          </div>

          {/* Navigation Arrows */}
          <button
            onClick={prevTestimonial}
            className="absolute -left-16 top-1/2 -translate-y-1/2 w-12 h-12 bg-white/80 backdrop-blur-sm rounded-full shadow-lg hover:shadow-xl transition-all duration-300 hidden lg:flex items-center justify-center text-text-primary hover:bg-white"
          >
            <ChevronLeft className="w-6 h-6" />
          </button>

          <button
            onClick={nextTestimonial}
            className="absolute -right-16 top-1/2 -translate-y-1/2 w-12 h-12 bg-white/80 backdrop-blur-sm rounded-full shadow-lg hover:shadow-xl transition-all duration-300 hidden lg:flex items-center justify-center text-text-primary hover:bg-white"
          >
            <ChevronRight className="w-6 h-6" />
          </button>
        </div>
      </AnimatedElement>

      {/* Stats Section */}
      <AnimatedElement animation="slideUp" delay={0.8} className="mt-24">
        <div className="grid grid-cols-2 md:grid-cols-4 gap-8 text-center">
          <div className="space-y-2">
            <div className="text-3xl md:text-4xl font-bold text-text-primary">50+</div>
            <div className="text-text-secondary">Happy Clients</div>
          </div>
          <div className="space-y-2">
            <div className="text-3xl md:text-4xl font-bold text-text-primary">5.0</div>
            <div className="text-text-secondary">Average Rating</div>
          </div>
          <div className="space-y-2">
            <div className="text-3xl md:text-4xl font-bold text-text-primary">100+</div>
            <div className="text-text-secondary">Makeup Sessions</div>
          </div>
          <div className="space-y-2">
            <div className="text-3xl md:text-4xl font-bold text-text-primary">7</div>
            <div className="text-text-secondary">Cities Served</div>
          </div>
        </div>
      </AnimatedElement>
    </Section>
  )
}
