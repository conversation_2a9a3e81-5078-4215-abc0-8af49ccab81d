# Cloudinary Signature Fix Guide

## 🚨 Problem: "Invalid Signature" Error

The signature error occurs when the parameters used to generate the signature don't exactly match the parameters sent in the upload request.

## ✅ **What I Fixed:**

### **1. Simplified Parameters**
- **Before**: Used many parameters (allowed_formats, max_file_size, etc.)
- **After**: Using only essential parameters (timestamp, folder)
- **Why**: Fewer parameters = less chance for mismatch

### **2. Exact Parameter Matching**
- **Signature generation** and **upload request** now use identical parameters
- **Parameter order** is consistent
- **Data types** match exactly (strings vs booleans)

### **3. Added Debugging**
- **Console logs** show signature generation process
- **Test API route** to verify configuration
- **Better error messages** with details

## 🔧 **Current Setup:**

### **Signature Parameters (Minimal):**
```javascript
{
  timestamp: 1234567890,
  folder: "gallery"
}
```

### **Upload Parameters (Same):**
```javascript
formData.append('timestamp', timestamp)
formData.append('folder', folder)
formData.append('signature', signature)
formData.append('api_key', api_key)
formData.append('file', file)
```

## 🧪 **Testing Steps:**

### **1. Test Configuration**
Visit: `http://localhost:3001/api/test-cloudinary`

**Expected Response:**
```json
{
  "status": "ok",
  "config": {
    "cloud_name": "your-cloud-name",
    "api_key": "Set",
    "api_secret": "Set"
  },
  "test_signature": {
    "params": { "timestamp": 1234567890, "folder": "test" },
    "signature": "abc123...",
    "string_to_sign": "folder=test&timestamp=1234567890"
  }
}
```

### **2. Check Environment Variables**
```bash
# In your .env.local file:
NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME="your-cloud-name"
CLOUDINARY_API_KEY="your-api-key"
CLOUDINARY_API_SECRET="your-api-secret"
```

### **3. Test Upload**
1. **Open browser console** (F12)
2. **Try uploading an image**
3. **Check console logs** for signature data
4. **Look for any error messages**

## 🔍 **Debugging Information**

### **Console Logs to Check:**
```javascript
// Should appear in browser console:
"Signature data received: { signature: '...', timestamp: 123, ... }"
"Upload parameters:"
"timestamp: 1234567890"
"folder: gallery"
"signature: abc123..."
"api_key: 123456..."
```

### **Server Logs to Check:**
```javascript
// Should appear in server console:
"Signature params: { timestamp: 1234567890, folder: 'gallery' }"
"Generated signature: abc123..."
```

## 🚨 **Common Issues & Fixes:**

### **Issue 1: Environment Variables Not Set**
**Error**: "CLOUDINARY_API_SECRET not configured"
**Fix**: 
```bash
# Add to .env.local:
CLOUDINARY_API_SECRET="your-actual-secret"
```

### **Issue 2: Wrong API Secret**
**Error**: "Invalid signature"
**Fix**: 
- Go to Cloudinary dashboard
- Copy the **exact** API secret
- Paste in .env.local without quotes inside the value

### **Issue 3: Timestamp Mismatch**
**Error**: "Invalid signature" 
**Fix**: 
- Signature and upload use the same timestamp
- Check console logs to verify

### **Issue 4: Parameter Order**
**Error**: "Invalid signature"
**Fix**: 
- Parameters are automatically sorted by Cloudinary
- Our code now uses minimal parameters to avoid issues

## 🔧 **Manual Verification:**

### **Test Signature Generation:**
```javascript
// Run in browser console on your CMS page:
fetch('/api/cloudinary-signature', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({ folder: 'test' })
})
.then(res => res.json())
.then(data => {
  console.log('Signature test:', data)
  // Should show signature, timestamp, api_key, etc.
})
```

### **Test Direct Upload:**
```javascript
// Test upload with curl (replace values):
curl -X POST https://api.cloudinary.com/v1_1/YOUR_CLOUD_NAME/image/upload \
  -F "file=@test-image.jpg" \
  -F "timestamp=1234567890" \
  -F "folder=test" \
  -F "signature=YOUR_SIGNATURE" \
  -F "api_key=YOUR_API_KEY"
```

## 📋 **Verification Checklist:**

- [ ] ✅ Environment variables are set correctly
- [ ] ✅ API secret is the correct one from Cloudinary
- [ ] ✅ Test API route returns "ok" status
- [ ] ✅ Console shows signature generation logs
- [ ] ✅ Upload parameters match signature parameters
- [ ] ✅ No extra parameters in upload request

## 🎯 **Expected Behavior:**

1. **Click upload button** → File selection dialog
2. **Select image** → Signature generation starts
3. **Console shows logs** → Signature data and upload parameters
4. **Progress bar appears** → Upload in progress
5. **Image preview shows** → Upload successful
6. **Check Cloudinary dashboard** → File appears in folder

## 📞 **If Still Not Working:**

### **Check These:**
1. **Restart your development server** after changing .env.local
2. **Clear browser cache** and try again
3. **Check Cloudinary dashboard** for any account issues
4. **Try with a very small image** (< 100KB) first
5. **Check browser network tab** for detailed error responses

### **Get Help:**
1. **Share the test API response**: Visit `/api/test-cloudinary`
2. **Share console logs**: From browser and server
3. **Share environment setup**: (without revealing actual secrets)

---

**🎉 The signature should now work correctly with minimal parameters!**

The simplified approach reduces the chance of parameter mismatches and makes debugging much easier.
