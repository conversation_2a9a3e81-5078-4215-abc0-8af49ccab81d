# Anjali CMS - Professional Makeup Artist Content Management System

A comprehensive, modern CMS built specifically for makeup artists to manage their business, showcase their work, and connect with clients.

## ✨ Features

### 🎨 **Content Management**
- **Blog System**: Rich text editor with SEO optimization
- **Services Management**: Showcase your makeup services with pricing
- **Packages**: Create bundled service offerings
- **Gallery**: Professional portfolio management
- **Testimonials**: Client reviews with approval workflow

### 🖼️ **Media Management**
- **Cloudinary Integration**: Professional image optimization
- **Automatic Resizing**: Images optimized for web delivery
- **Organized Storage**: Separate folders for different content types

### 🔧 **Admin Features**
- **Modern Dashboard**: Intuitive admin interface
- **User Management**: Role-based access control
- **Site Settings**: Comprehensive configuration options
- **Data Import/Export**: Backup and migration tools
- **SEO Tools**: Built-in search engine optimization

### 📱 **Technical Excellence**
- **Next.js 14**: Latest App Router with TypeScript
- **Responsive Design**: Works perfectly on all devices
- **Security**: Authentication and authorization
- **Performance**: Optimized for speed and efficiency

## 🚀 Quick Start

### Prerequisites
- Node.js 18+ 
- PostgreSQL database
- Cloudinary account (for image management)

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd anjali-cms
   ```

2. **Run the setup script**
   ```bash
   node scripts/setup.js
   ```

3. **Configure environment variables**
   Update the `.env` file with your actual credentials:
   ```env
   DATABASE_URL="postgresql://username:password@localhost:5432/anjali_cms"
   NEXTAUTH_SECRET="your-secret-key-here"
   NEXTAUTH_URL="http://localhost:3000"
   NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME="your-cloud-name"
   CLOUDINARY_API_KEY="your-api-key"
   CLOUDINARY_API_SECRET="your-api-secret"
   ```

4. **Set up the database**
   ```bash
   npm run db:push
   npm run db:seed
   ```

5. **Start the development server**
   ```bash
   npm run dev
   ```

6. **Access the admin panel**
   - URL: http://localhost:3000/dashboard
   - Email: <EMAIL>
   - Password: admin123

## 📚 Documentation

### Database Commands
```bash
npm run db:generate    # Generate Prisma client
npm run db:push        # Push schema to database
npm run db:migrate     # Create and run migrations
npm run db:seed        # Populate with sample data
npm run db:reset       # Reset database and reseed
npm run db:studio      # Open Prisma Studio
```

### Development Commands
```bash
npm run dev           # Start development server
npm run build         # Build for production
npm run start         # Start production server
npm run lint          # Run ESLint
```

## 🏗️ Project Structure

```
anjali-cms/
├── src/
│   ├── app/
│   │   ├── dashboard/          # Admin interface
│   │   │   ├── blogs/          # Blog management
│   │   │   ├── services/       # Service management
│   │   │   ├── packages/       # Package management
│   │   │   ├── testimonials/   # Testimonial management
│   │   │   ├── gallery/        # Gallery management
│   │   │   ├── settings/       # Site configuration
│   │   │   └── data/           # Import/export
│   │   ├── api/                # REST API endpoints
│   │   └── auth/               # Authentication
│   ├── components/
│   │   ├── ui/                 # shadcn/ui components
│   │   ├── forms/              # Form components
│   │   ├── dashboard/          # Dashboard components
│   │   └── editor/             # Rich text editor
│   ├── lib/                    # Utilities and config
│   └── types/                  # TypeScript definitions
├── prisma/                     # Database schema and migrations
├── scripts/                    # Setup and utility scripts
└── API_DOCUMENTATION.md        # Complete API documentation
```

## 🔧 Configuration

### Cloudinary Setup
1. Create a Cloudinary account at https://cloudinary.com
2. Get your cloud name, API key, and API secret
3. Add them to your `.env` file

### Database Setup
1. Install PostgreSQL
2. Create a database named `anjali_cms`
3. Update the `DATABASE_URL` in your `.env` file

### Email Configuration (Optional)
For contact forms and notifications:
```env
EMAIL_SERVER_HOST="smtp.gmail.com"
EMAIL_SERVER_PORT=587
EMAIL_SERVER_USER="<EMAIL>"
EMAIL_SERVER_PASSWORD="your-app-password"
EMAIL_FROM="<EMAIL>"
```

## 📖 Usage Guide

### Managing Content
1. **Blogs**: Create and publish blog posts with rich text content
2. **Services**: Add your makeup services with descriptions and pricing
3. **Packages**: Create bundled service offerings
4. **Gallery**: Upload and organize your portfolio images
5. **Testimonials**: Manage client reviews and feedback

### Site Configuration
- Navigate to Settings to configure your business information
- Update contact details, social media links, and SEO settings
- Customize your site's appearance and functionality

### Data Management
- Use the Data Management section to import/export content
- Regular backups are recommended
- Import existing data from JSON files

## 🛠️ Customization

### Adding New Content Types
1. Update the Prisma schema in `prisma/schema.prisma`
2. Create API routes in `src/app/api/`
3. Add dashboard pages in `src/app/dashboard/`
4. Create form components in `src/components/forms/`

### Styling
- The project uses Tailwind CSS for styling
- Components are built with shadcn/ui
- Customize the theme in `tailwind.config.js`

## 🔒 Security

- Authentication via NextAuth.js
- Role-based access control
- Input validation with Zod
- SQL injection protection via Prisma
- XSS protection built-in

## 📈 Performance

- Image optimization via Cloudinary
- Database query optimization
- Lazy loading and code splitting
- Caching strategies implemented

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License.

## 🆘 Support

For support and questions:
- Check the API documentation in `API_DOCUMENTATION.md`
- Review the project structure above
- Create an issue in the repository

## 🎯 Roadmap

- [ ] Multi-language support
- [ ] Advanced analytics
- [ ] Client booking system
- [ ] Payment integration
- [ ] Mobile app
- [ ] Advanced SEO tools

---

Built with ❤️ for makeup artists who want to showcase their artistry professionally.
