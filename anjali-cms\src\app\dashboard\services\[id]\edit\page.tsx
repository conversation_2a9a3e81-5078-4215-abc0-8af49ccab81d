"use client"

import { useState, useEffect } from 'react'
import { useParams } from 'next/navigation'
import { ServiceForm } from '@/components/forms/service-form'
import { toast } from 'sonner'

interface Service {
  id: string
  title: string
  slug: string
  description: string
  features: string[]
  duration?: string
  price?: string
  image?: string
  category?: string
  popular: boolean
  status: 'ACTIVE' | 'INACTIVE' | 'ARCHIVED'
}

export default function EditServicePage() {
  const params = useParams()
  const [service, setService] = useState<Service | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const fetchService = async () => {
      try {
        const response = await fetch(`/api/services/${params.id}`)
        if (response.ok) {
          const data = await response.json()
          setService(data)
        } else {
          toast.error('Service not found')
        }
      } catch (error) {
        toast.error('Error fetching service')
      } finally {
        setLoading(false)
      }
    }

    if (params.id) {
      fetchService()
    }
  }, [params.id])

  if (loading) {
    return <div>Loading...</div>
  }

  if (!service) {
    return <div>Service not found</div>
  }

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold">Edit Service</h1>
        <p className="text-muted-foreground">
          Update your service details and settings
        </p>
      </div>

      <ServiceForm initialData={service} isEditing />
    </div>
  )
}
