'use client'

import Link from 'next/link'
import { Phone, Mail, MapPin, Clock, MessageCircle, Facebook, Instagram } from 'lucide-react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Section, SectionHeader } from '@/components/ui/section'
import { AnimatedElement, StaggeredContainer, StaggeredItem } from '@/components/ui/animated-element'
import { getSiteConfig, getSocialLinks } from '@/lib/data'
import { generateWhatsAppLink } from '@/lib/utils'

export default function ContactInfo() {
  const siteConfig = getSiteConfig()
  const socialLinks = getSocialLinks()
  const whatsappLink = generateWhatsAppLink(
    siteConfig.contact.whatsapp,
    siteConfig.whatsappMessage
  )

  return (
    <Section>
      <AnimatedElement animation="fadeIn">
        <SectionHeader
          subtitle="Get In Touch"
          title="Contact Information"
          description="Multiple ways to reach us for bookings, consultations, and inquiries. We're here to help you with all your makeup needs."
        />
      </AnimatedElement>

      <StaggeredContainer className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
        {/* Phone Contact */}
        <StaggeredItem>
          <Card className="h-full hover:shadow-lg transition-shadow duration-300">
            <CardHeader className="text-center">
              <div className="w-16 h-16 bg-gradient-to-br from-rose-gold to-blush-pink rounded-full flex items-center justify-center mx-auto mb-4">
                <Phone className="w-8 h-8 text-white" />
              </div>
              <CardTitle className="text-xl">Phone & WhatsApp</CardTitle>
            </CardHeader>
            <CardContent className="text-center space-y-4">
              <p className="text-text-secondary">
                Call or message us directly for immediate assistance
              </p>
              <div className="space-y-2">
                <p className="font-semibold text-text-primary">{siteConfig.contact.phone}</p>
                <p className="text-text-secondary text-sm">Available during business hours</p>
              </div>
              <div className="space-y-2">
                <Button asChild variant="gradient" size="sm" className="w-full">
                  <Link href={whatsappLink} target="_blank" rel="noopener noreferrer">
                    <MessageCircle className="w-4 h-4 mr-2" />
                    WhatsApp Now
                  </Link>
                </Button>
                <Button asChild variant="outline" size="sm" className="w-full">
                  <Link href={`tel:${siteConfig.contact.phone}`}>
                    <Phone className="w-4 h-4 mr-2" />
                    Call Now
                  </Link>
                </Button>
              </div>
            </CardContent>
          </Card>
        </StaggeredItem>

        {/* Email Contact */}
        <StaggeredItem>
          <Card className="h-full hover:shadow-lg transition-shadow duration-300">
            <CardHeader className="text-center">
              <div className="w-16 h-16 bg-gradient-to-br from-blush-pink to-lavender rounded-full flex items-center justify-center mx-auto mb-4">
                <Mail className="w-8 h-8 text-white" />
              </div>
              <CardTitle className="text-xl">Email</CardTitle>
            </CardHeader>
            <CardContent className="text-center space-y-4">
              <p className="text-text-secondary">
                Send us detailed inquiries and we'll respond promptly
              </p>
              <div className="space-y-2">
                <p className="font-semibold text-text-primary">{siteConfig.contact.email}</p>
                <p className="text-text-secondary text-sm">Response within 24 hours</p>
              </div>
              <Button asChild variant="outline" size="sm" className="w-full">
                <Link href={`mailto:${siteConfig.contact.email}`}>
                  <Mail className="w-4 h-4 mr-2" />
                  Send Email
                </Link>
              </Button>
            </CardContent>
          </Card>
        </StaggeredItem>

        {/* Location */}
        <StaggeredItem>
          <Card className="h-full hover:shadow-lg transition-shadow duration-300">
            <CardHeader className="text-center">
              <div className="w-16 h-16 bg-gradient-to-br from-lavender to-rose-gold rounded-full flex items-center justify-center mx-auto mb-4">
                <MapPin className="w-8 h-8 text-white" />
              </div>
              <CardTitle className="text-xl">Location</CardTitle>
            </CardHeader>
            <CardContent className="text-center space-y-4">
              <p className="text-text-secondary">
                Visit our studio or we can come to you
              </p>
              <div className="space-y-2">
                <p className="font-semibold text-text-primary">{siteConfig.contact.address.street}</p>
                <p className="text-text-secondary">
                  {siteConfig.contact.address.city}, {siteConfig.contact.address.state}
                </p>
                <p className="text-text-secondary">{siteConfig.contact.address.country}</p>
              </div>
              <Button variant="outline" size="sm" className="w-full" disabled>
                <MapPin className="w-4 h-4 mr-2" />
                View on Map
              </Button>
            </CardContent>
          </Card>
        </StaggeredItem>
      </StaggeredContainer>

      {/* Business Hours & Social Media */}
      <div className="grid md:grid-cols-2 gap-8 mt-16">
        {/* Business Hours */}
        <AnimatedElement animation="slideRight" delay={0.6}>
          <Card className="bg-gradient-to-br from-cream to-soft-gray border-0">
            <CardHeader>
              <div className="flex items-center gap-3">
                <div className="w-12 h-12 bg-gradient-to-br from-rose-gold to-blush-pink rounded-full flex items-center justify-center">
                  <Clock className="w-6 h-6 text-white" />
                </div>
                <CardTitle className="text-xl">Business Hours</CardTitle>
              </div>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="flex justify-between items-center">
                <span className="text-text-secondary">Monday - Friday</span>
                <span className="font-semibold text-text-primary">9:00 AM - 6:00 PM</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-text-secondary">Saturday</span>
                <span className="font-semibold text-text-primary">9:00 AM - 6:00 PM</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-text-secondary">Sunday</span>
                <span className="font-semibold text-text-primary">10:00 AM - 4:00 PM</span>
              </div>
              <div className="pt-3 border-t border-gray-200">
                <p className="text-sm text-text-muted">
                  Emergency bookings available with advance notice. 
                  Early morning and evening appointments can be arranged.
                </p>
              </div>
            </CardContent>
          </Card>
        </AnimatedElement>

        {/* Social Media */}
        <AnimatedElement animation="slideLeft" delay={0.8}>
          <Card className="bg-gradient-to-br from-rose-gold/10 to-blush-pink/10 border-0">
            <CardHeader>
              <CardTitle className="text-xl">Follow Us</CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <p className="text-text-secondary">
                Stay connected with us on social media for daily beauty tips, 
                behind-the-scenes content, and our latest work.
              </p>
              
              <div className="space-y-4">
                <Link 
                  href={socialLinks.instagram}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="flex items-center gap-4 p-3 rounded-lg hover:bg-white/50 transition-colors group"
                >
                  <div className="w-10 h-10 bg-gradient-to-br from-pink-500 to-purple-600 rounded-lg flex items-center justify-center">
                    <Instagram className="w-5 h-5 text-white" />
                  </div>
                  <div>
                    <div className="font-semibold text-text-primary group-hover:text-rose-gold-dark transition-colors">
                      Instagram
                    </div>
                    <div className="text-text-secondary text-sm">@anjalimakeup</div>
                  </div>
                </Link>

                <Link 
                  href={socialLinks.facebook}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="flex items-center gap-4 p-3 rounded-lg hover:bg-white/50 transition-colors group"
                >
                  <div className="w-10 h-10 bg-blue-600 rounded-lg flex items-center justify-center">
                    <Facebook className="w-5 h-5 text-white" />
                  </div>
                  <div>
                    <div className="font-semibold text-text-primary group-hover:text-rose-gold-dark transition-colors">
                      Facebook
                    </div>
                    <div className="text-text-secondary text-sm">Anjali Makeup Artist</div>
                  </div>
                </Link>
              </div>

              <div className="pt-4 border-t border-gray-200">
                <p className="text-sm text-text-muted">
                  Follow us for makeup tutorials, client transformations, 
                  and exclusive behind-the-scenes content.
                </p>
              </div>
            </CardContent>
          </Card>
        </AnimatedElement>
      </div>
    </Section>
  )
}
