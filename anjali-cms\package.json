{"name": "anjali-cms", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:seed": "tsx prisma/seed.ts", "db:reset": "prisma migrate reset --force && npm run db:seed", "db:studio": "prisma studio"}, "dependencies": {"@auth/prisma-adapter": "^2.10.0", "@hookform/resolvers": "^5.1.1", "@prisma/client": "^6.12.0", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-tooltip": "^1.2.7", "@tanstack/react-query": "^5.83.0", "@tanstack/react-query-devtools": "^5.83.0", "@tiptap/extension-bullet-list": "^3.0.6", "@tiptap/extension-color": "^3.0.6", "@tiptap/extension-image": "^3.0.6", "@tiptap/extension-link": "^3.0.6", "@tiptap/extension-list-item": "^3.0.6", "@tiptap/extension-ordered-list": "^3.0.6", "@tiptap/extension-table": "^3.0.6", "@tiptap/extension-table-cell": "^3.0.6", "@tiptap/extension-table-header": "^3.0.6", "@tiptap/extension-table-row": "^3.0.6", "@tiptap/extension-text-style": "^3.0.6", "@tiptap/react": "^3.0.6", "@tiptap/starter-kit": "^3.0.6", "@types/bcryptjs": "^2.4.6", "@types/pg": "^8.15.4", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "cloudinary": "^2.7.0", "clsx": "^2.1.1", "lucide-react": "^0.525.0", "next": "15.4.1", "next-auth": "^4.24.11", "next-themes": "^0.4.6", "pg": "^8.16.3", "prisma": "^6.12.0", "react": "19.1.0", "react-dom": "19.1.0", "react-hook-form": "^7.60.0", "sonner": "^2.0.6", "tailwind-merge": "^3.3.1", "zod": "^4.0.5"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.4.1", "tailwindcss": "^4", "tsx": "^4.19.2", "tw-animate-css": "^1.3.5", "typescript": "^5"}, "prisma": {"seed": "tsx prisma/seed.ts"}}