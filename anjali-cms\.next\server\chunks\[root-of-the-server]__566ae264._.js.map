{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 154, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/anjali-portfolio/anjali-cms/src/lib/prisma.ts"], "sourcesContent": ["import { PrismaClient } from '@prisma/client'\n\nconst globalForPrisma = globalThis as unknown as {\n  prisma: PrismaClient | undefined\n}\n\nexport const prisma = globalForPrisma.prisma ?? new PrismaClient()\n\nif (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,kBAAkB;AAIjB,MAAM,SAAS,gBAAgB,MAAM,IAAI,IAAI,6HAAA,CAAA,eAAY;AAEhE,wCAA2C,gBAAgB,MAAM,GAAG", "debugId": null}}, {"offset": {"line": 166, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/anjali-portfolio/anjali-cms/src/lib/auth.ts"], "sourcesContent": ["import { NextAuthOptions } from \"next-auth\"\nimport Cred<PERSON><PERSON><PERSON>rovider from \"next-auth/providers/credentials\"\nimport { PrismaAdapter } from \"@auth/prisma-adapter\"\nimport { prisma } from \"./prisma\"\nimport bcrypt from \"bcryptjs\"\n\nexport const authOptions: NextAuthOptions = {\n  adapter: PrismaAdapter(prisma) as any,\n  providers: [\n    CredentialsProvider({\n      name: \"credentials\",\n      credentials: {\n        email: { label: \"Email\", type: \"email\" },\n        password: { label: \"Password\", type: \"password\" }\n      },\n      async authorize(credentials) {\n        if (!credentials?.email || !credentials?.password) {\n          return null\n        }\n\n        const user = await prisma.user.findUnique({\n          where: {\n            email: credentials.email\n          }\n        })\n\n        if (!user) {\n          return null\n        }\n\n        const isPasswordValid = await bcrypt.compare(\n          credentials.password,\n          user.password\n        )\n\n        if (!isPasswordValid) {\n          return null\n        }\n\n        return {\n          id: user.id,\n          email: user.email,\n          name: user.name,\n          role: user.role,\n        }\n      }\n    })\n  ],\n  session: {\n    strategy: \"jwt\"\n  },\n  callbacks: {\n    async jwt({ token, user }) {\n      if (user) {\n        token.role = user.role\n      }\n      return token\n    },\n    async session({ session, token }) {\n      if (token) {\n        session.user.id = token.sub!\n        session.user.role = token.role as string\n      }\n      return session\n    }\n  },\n  pages: {\n    signIn: \"/auth/signin\",\n  }\n}\n"], "names": [], "mappings": ";;;AACA;AACA;AACA;AACA;;;;;AAEO,MAAM,cAA+B;IAC1C,SAAS,CAAA,GAAA,sJAAA,CAAA,gBAAa,AAAD,EAAE,sHAAA,CAAA,SAAM;IAC7B,WAAW;QACT,CAAA,GAAA,0JAAA,CAAA,UAAmB,AAAD,EAAE;YAClB,MAAM;YACN,aAAa;gBACX,OAAO;oBAAE,OAAO;oBAAS,MAAM;gBAAQ;gBACvC,UAAU;oBAAE,OAAO;oBAAY,MAAM;gBAAW;YAClD;YACA,MAAM,WAAU,WAAW;gBACzB,IAAI,CAAC,aAAa,SAAS,CAAC,aAAa,UAAU;oBACjD,OAAO;gBACT;gBAEA,MAAM,OAAO,MAAM,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,UAAU,CAAC;oBACxC,OAAO;wBACL,OAAO,YAAY,KAAK;oBAC1B;gBACF;gBAEA,IAAI,CAAC,MAAM;oBACT,OAAO;gBACT;gBAEA,MAAM,kBAAkB,MAAM,mIAAA,CAAA,UAAM,CAAC,OAAO,CAC1C,YAAY,QAAQ,EACpB,KAAK,QAAQ;gBAGf,IAAI,CAAC,iBAAiB;oBACpB,OAAO;gBACT;gBAEA,OAAO;oBACL,IAAI,KAAK,EAAE;oBACX,OAAO,KAAK,KAAK;oBACjB,MAAM,KAAK,IAAI;oBACf,MAAM,KAAK,IAAI;gBACjB;YACF;QACF;KACD;IACD,SAAS;QACP,UAAU;IACZ;IACA,WAAW;QACT,MAAM,KAAI,EAAE,KAAK,EAAE,IAAI,EAAE;YACvB,IAAI,MAAM;gBACR,MAAM,IAAI,GAAG,KAAK,IAAI;YACxB;YACA,OAAO;QACT;QACA,MAAM,SAAQ,EAAE,OAAO,EAAE,KAAK,EAAE;YAC9B,IAAI,OAAO;gBACT,QAAQ,IAAI,CAAC,EAAE,GAAG,MAAM,GAAG;gBAC3B,QAAQ,IAAI,CAAC,IAAI,GAAG,MAAM,IAAI;YAChC;YACA,OAAO;QACT;IACF;IACA,OAAO;QACL,QAAQ;IACV;AACF", "debugId": null}}, {"offset": {"line": 243, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/anjali-portfolio/anjali-cms/src/lib/cors.ts"], "sourcesContent": ["import { NextResponse } from 'next/server'\n\nexport const corsHeaders = {\n  'Access-Control-Allow-Origin': '*',\n  'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',\n  'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-Requested-With',\n  'Access-Control-Max-Age': '86400',\n}\n\nexport function withCors(response: NextResponse) {\n  Object.entries(corsHeaders).forEach(([key, value]) => {\n    response.headers.set(key, value)\n  })\n  return response\n}\n\nexport function corsResponse(data: any, init?: ResponseInit) {\n  return NextResponse.json(data, {\n    ...init,\n    headers: {\n      ...corsHeaders,\n      ...init?.headers,\n    },\n  })\n}\n\nexport function corsOptionsResponse() {\n  return new NextResponse(null, {\n    status: 200,\n    headers: corsHeaders,\n  })\n}\n"], "names": [], "mappings": ";;;;;;AAAA;;AAEO,MAAM,cAAc;IACzB,+BAA+B;IAC/B,gCAAgC;IAChC,gCAAgC;IAChC,0BAA0B;AAC5B;AAEO,SAAS,SAAS,QAAsB;IAC7C,OAAO,OAAO,CAAC,aAAa,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;QAC/C,SAAS,OAAO,CAAC,GAAG,CAAC,KAAK;IAC5B;IACA,OAAO;AACT;AAEO,SAAS,aAAa,IAAS,EAAE,IAAmB;IACzD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC,MAAM;QAC7B,GAAG,IAAI;QACP,SAAS;YACP,GAAG,WAAW;YACd,GAAG,MAAM,OAAO;QAClB;IACF;AACF;AAEO,SAAS;IACd,OAAO,IAAI,gIAAA,CAAA,eAAY,CAAC,MAAM;QAC5B,QAAQ;QACR,SAAS;IACX;AACF", "debugId": null}}, {"offset": {"line": 282, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/anjali-portfolio/anjali-cms/src/app/api/settings/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\nimport { getServerSession } from 'next-auth'\nimport { authOptions } from '@/lib/auth'\nimport { prisma } from '@/lib/prisma'\nimport { z } from 'zod'\nimport { corsResponse, corsOptionsResponse } from '@/lib/cors'\n\nconst settingSchema = z.object({\n  key: z.string().min(1, 'Key is required'),\n  value: z.string().min(1, 'Value is required'),\n  type: z.enum(['TEXT', 'JSON', 'BOOLEAN', 'NUMBER', 'URL', 'EMAIL']).default('TEXT'),\n  description: z.string().optional(),\n})\n\nexport async function OPTIONS() {\n  return corsOptionsResponse()\n}\n\nexport async function GET(request: NextRequest) {\n  try {\n    // Public endpoint - no authentication required for reading settings\n\n    const { searchParams } = new URL(request.url)\n    const key = searchParams.get('key')\n\n    if (key) {\n      // Get specific setting\n      const setting = await prisma.siteConfig.findUnique({\n        where: { key },\n      })\n\n      if (!setting) {\n        return NextResponse.json({ error: 'Setting not found' }, { status: 404 })\n      }\n\n      return corsResponse(setting)\n    } else {\n      // Get all settings\n      const settings = await prisma.siteConfig.findMany({\n        orderBy: { key: 'asc' },\n      })\n\n      // Group settings by category for better organization\n      const groupedSettings = settings.reduce((acc, setting) => {\n        const category = setting.key.split('.')[0] || 'general'\n        if (!acc[category]) {\n          acc[category] = []\n        }\n        acc[category].push(setting)\n        return acc\n      }, {} as Record<string, typeof settings>)\n\n      return corsResponse({\n        settings,\n        grouped: groupedSettings,\n      })\n    }\n  } catch (error) {\n    console.error('Error fetching settings:', error)\n    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })\n  }\n}\n\nexport async function POST(request: NextRequest) {\n  try {\n    const session = await getServerSession(authOptions)\n    \n    if (!session) {\n      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })\n    }\n\n    const body = await request.json()\n    const validatedData = settingSchema.parse(body)\n\n    // Check if setting already exists\n    const existingSetting = await prisma.siteConfig.findUnique({\n      where: { key: validatedData.key },\n    })\n\n    if (existingSetting) {\n      return NextResponse.json({ error: 'Setting with this key already exists' }, { status: 400 })\n    }\n\n    const setting = await prisma.siteConfig.create({\n      data: validatedData,\n    })\n\n    return NextResponse.json(setting, { status: 201 })\n  } catch (error) {\n    if (error instanceof z.ZodError) {\n      return NextResponse.json({ error: error.errors }, { status: 400 })\n    }\n    \n    console.error('Error creating setting:', error)\n    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })\n  }\n}\n\nexport async function PUT(request: NextRequest) {\n  try {\n    const session = await getServerSession(authOptions)\n    \n    if (!session) {\n      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })\n    }\n\n    const body = await request.json()\n    \n    // Handle bulk update\n    if (Array.isArray(body)) {\n      const updates = body.map(async (item) => {\n        const { key, value, type, description } = item\n        return prisma.siteConfig.upsert({\n          where: { key },\n          update: { value, type, description },\n          create: { key, value, type: type || 'TEXT', description },\n        })\n      })\n\n      const results = await Promise.all(updates)\n      return NextResponse.json(results)\n    }\n\n    // Handle single update\n    const { key, value, type, description } = body\n\n    if (!key || !value) {\n      return NextResponse.json({ error: 'Key and value are required' }, { status: 400 })\n    }\n\n    const setting = await prisma.siteConfig.upsert({\n      where: { key },\n      update: { value, type, description },\n      create: { key, value, type: type || 'TEXT', description },\n    })\n\n    return NextResponse.json(setting)\n  } catch (error) {\n    console.error('Error updating settings:', error)\n    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })\n  }\n}\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;;;;;;;AAEA,MAAM,gBAAgB,+KAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC7B,KAAK,+KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IACvB,OAAO,+KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IACzB,MAAM,+KAAA,CAAA,IAAC,CAAC,IAAI,CAAC;QAAC;QAAQ;QAAQ;QAAW;QAAU;QAAO;KAAQ,EAAE,OAAO,CAAC;IAC5E,aAAa,+KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;AAClC;AAEO,eAAe;IACpB,OAAO,CAAA,GAAA,oHAAA,CAAA,sBAAmB,AAAD;AAC3B;AAEO,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,oEAAoE;QAEpE,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;QAC5C,MAAM,MAAM,aAAa,GAAG,CAAC;QAE7B,IAAI,KAAK;YACP,uBAAuB;YACvB,MAAM,UAAU,MAAM,sHAAA,CAAA,SAAM,CAAC,UAAU,CAAC,UAAU,CAAC;gBACjD,OAAO;oBAAE;gBAAI;YACf;YAEA,IAAI,CAAC,SAAS;gBACZ,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;oBAAE,OAAO;gBAAoB,GAAG;oBAAE,QAAQ;gBAAI;YACzE;YAEA,OAAO,CAAA,GAAA,oHAAA,CAAA,eAAY,AAAD,EAAE;QACtB,OAAO;YACL,mBAAmB;YACnB,MAAM,WAAW,MAAM,sHAAA,CAAA,SAAM,CAAC,UAAU,CAAC,QAAQ,CAAC;gBAChD,SAAS;oBAAE,KAAK;gBAAM;YACxB;YAEA,qDAAqD;YACrD,MAAM,kBAAkB,SAAS,MAAM,CAAC,CAAC,KAAK;gBAC5C,MAAM,WAAW,QAAQ,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,IAAI;gBAC9C,IAAI,CAAC,GAAG,CAAC,SAAS,EAAE;oBAClB,GAAG,CAAC,SAAS,GAAG,EAAE;gBACpB;gBACA,GAAG,CAAC,SAAS,CAAC,IAAI,CAAC;gBACnB,OAAO;YACT,GAAG,CAAC;YAEJ,OAAO,CAAA,GAAA,oHAAA,CAAA,eAAY,AAAD,EAAE;gBAClB;gBACA,SAAS;YACX;QACF;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4BAA4B;QAC1C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YAAE,OAAO;QAAwB,GAAG;YAAE,QAAQ;QAAI;IAC7E;AACF;AAEO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,UAAU,MAAM,CAAA,GAAA,uIAAA,CAAA,mBAAgB,AAAD,EAAE,oHAAA,CAAA,cAAW;QAElD,IAAI,CAAC,SAAS;YACZ,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAe,GAAG;gBAAE,QAAQ;YAAI;QACpE;QAEA,MAAM,OAAO,MAAM,QAAQ,IAAI;QAC/B,MAAM,gBAAgB,cAAc,KAAK,CAAC;QAE1C,kCAAkC;QAClC,MAAM,kBAAkB,MAAM,sHAAA,CAAA,SAAM,CAAC,UAAU,CAAC,UAAU,CAAC;YACzD,OAAO;gBAAE,KAAK,cAAc,GAAG;YAAC;QAClC;QAEA,IAAI,iBAAiB;YACnB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAuC,GAAG;gBAAE,QAAQ;YAAI;QAC5F;QAEA,MAAM,UAAU,MAAM,sHAAA,CAAA,SAAM,CAAC,UAAU,CAAC,MAAM,CAAC;YAC7C,MAAM;QACR;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC,SAAS;YAAE,QAAQ;QAAI;IAClD,EAAE,OAAO,OAAO;QACd,IAAI,iBAAiB,+KAAA,CAAA,IAAC,CAAC,QAAQ,EAAE;YAC/B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO,MAAM,MAAM;YAAC,GAAG;gBAAE,QAAQ;YAAI;QAClE;QAEA,QAAQ,KAAK,CAAC,2BAA2B;QACzC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YAAE,OAAO;QAAwB,GAAG;YAAE,QAAQ;QAAI;IAC7E;AACF;AAEO,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,MAAM,UAAU,MAAM,CAAA,GAAA,uIAAA,CAAA,mBAAgB,AAAD,EAAE,oHAAA,CAAA,cAAW;QAElD,IAAI,CAAC,SAAS;YACZ,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAe,GAAG;gBAAE,QAAQ;YAAI;QACpE;QAEA,MAAM,OAAO,MAAM,QAAQ,IAAI;QAE/B,qBAAqB;QACrB,IAAI,MAAM,OAAO,CAAC,OAAO;YACvB,MAAM,UAAU,KAAK,GAAG,CAAC,OAAO;gBAC9B,MAAM,EAAE,GAAG,EAAE,KAAK,EAAE,IAAI,EAAE,WAAW,EAAE,GAAG;gBAC1C,OAAO,sHAAA,CAAA,SAAM,CAAC,UAAU,CAAC,MAAM,CAAC;oBAC9B,OAAO;wBAAE;oBAAI;oBACb,QAAQ;wBAAE;wBAAO;wBAAM;oBAAY;oBACnC,QAAQ;wBAAE;wBAAK;wBAAO,MAAM,QAAQ;wBAAQ;oBAAY;gBAC1D;YACF;YAEA,MAAM,UAAU,MAAM,QAAQ,GAAG,CAAC;YAClC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;QAC3B;QAEA,uBAAuB;QACvB,MAAM,EAAE,GAAG,EAAE,KAAK,EAAE,IAAI,EAAE,WAAW,EAAE,GAAG;QAE1C,IAAI,CAAC,OAAO,CAAC,OAAO;YAClB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAA6B,GAAG;gBAAE,QAAQ;YAAI;QAClF;QAEA,MAAM,UAAU,MAAM,sHAAA,CAAA,SAAM,CAAC,UAAU,CAAC,MAAM,CAAC;YAC7C,OAAO;gBAAE;YAAI;YACb,QAAQ;gBAAE;gBAAO;gBAAM;YAAY;YACnC,QAAQ;gBAAE;gBAAK;gBAAO,MAAM,QAAQ;gBAAQ;YAAY;QAC1D;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;IAC3B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4BAA4B;QAC1C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YAAE,OAAO;QAAwB,GAAG;YAAE,QAAQ;QAAI;IAC7E;AACF", "debugId": null}}]}